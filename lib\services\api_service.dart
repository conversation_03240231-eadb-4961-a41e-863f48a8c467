import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'http://localhost/admin/api';
  
  // Singleton pattern
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  Map<String, String> _headersWithAuth(String token) => {
    ..._headers,
    'Authorization': 'Bearer $token',
  };

  // Auth endpoints
  Future<Map<String, dynamic>> login(String email, String password, String userType) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth.php?action=login'),
        headers: _headers,
        body: jsonEncode({
          'email': email,
          'password': password,
          'user_type': userType,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> register(Map<String, dynamic> userData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth.php?action=register'),
        headers: _headers,
        body: jsonEncode(userData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Orders endpoints
  Future<Map<String, dynamic>> getOrders(String userType, String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/orders.php?user_type=$userType&user_id=$userId'),
        headers: _headers,
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> createOrder(Map<String, dynamic> orderData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/orders.php'),
        headers: _headers,
        body: jsonEncode(orderData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> getOrderById(String orderId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/orders.php?order_id=$orderId'),
        headers: _headers,
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Offers endpoints
  Future<Map<String, dynamic>> submitOffer(Map<String, dynamic> offerData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/offers.php'),
        headers: _headers,
        body: jsonEncode(offerData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> getOffersByOrder(String orderId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/offers.php?order_id=$orderId'),
        headers: _headers,
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Messages endpoints
  Future<Map<String, dynamic>> sendMessage(Map<String, dynamic> messageData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/messages.php'),
        headers: _headers,
        body: jsonEncode(messageData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> getMessagesByOrder(String orderId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/messages.php?order_id=$orderId'),
        headers: _headers,
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Invoice endpoints
  Future<Map<String, dynamic>> createInvoice(String token, Map<String, dynamic> invoiceData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/invoices'),
        headers: _headersWithAuth(token),
        body: jsonEncode(invoiceData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> uploadPaymentReceipt(String token, String invoiceId, String imagePath) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/invoices/$invoiceId/payment-receipt'),
      );
      
      request.headers.addAll(_headersWithAuth(token));
      request.files.add(await http.MultipartFile.fromPath('receipt', imagePath));

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // File upload
  Future<Map<String, dynamic>> uploadImage(String token, String imagePath) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/upload'),
      );
      
      request.headers.addAll(_headersWithAuth(token));
      request.files.add(await http.MultipartFile.fromPath('image', imagePath));

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Map<String, dynamic> _handleResponse(http.Response response) {
    final Map<String, dynamic> data = jsonDecode(response.body);
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return data;
    } else {
      throw Exception(data['message'] ?? 'Unknown error occurred');
    }
  }
}
