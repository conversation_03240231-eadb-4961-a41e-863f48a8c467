import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'https://your-api-url.com/api';
  
  // Singleton pattern
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  Map<String, String> _headersWithAuth(String token) => {
    ..._headers,
    'Authorization': 'Bearer $token',
  };

  // Auth endpoints
  Future<Map<String, dynamic>> login(String email, String password, String userType) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/login'),
        headers: _headers,
        body: jsonEncode({
          'email': email,
          'password': password,
          'user_type': userType,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> register(Map<String, dynamic> userData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/register'),
        headers: _headers,
        body: jsonEncode(userData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Orders endpoints
  Future<Map<String, dynamic>> getOrders(String token, {String? userType}) async {
    try {
      String url = '$baseUrl/orders';
      if (userType != null) {
        url += '?user_type=$userType';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: _headersWithAuth(token),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> createOrder(String token, Map<String, dynamic> orderData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/orders'),
        headers: _headersWithAuth(token),
        body: jsonEncode(orderData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> getOrderById(String token, String orderId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/orders/$orderId'),
        headers: _headersWithAuth(token),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Offers endpoints
  Future<Map<String, dynamic>> submitOffer(String token, Map<String, dynamic> offerData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/offers'),
        headers: _headersWithAuth(token),
        body: jsonEncode(offerData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> getOffersByOrder(String token, String orderId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/orders/$orderId/offers'),
        headers: _headersWithAuth(token),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Messages endpoints
  Future<Map<String, dynamic>> sendMessage(String token, Map<String, dynamic> messageData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/messages'),
        headers: _headersWithAuth(token),
        body: jsonEncode(messageData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> getMessagesByOrder(String token, String orderId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/orders/$orderId/messages'),
        headers: _headersWithAuth(token),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Invoice endpoints
  Future<Map<String, dynamic>> createInvoice(String token, Map<String, dynamic> invoiceData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/invoices'),
        headers: _headersWithAuth(token),
        body: jsonEncode(invoiceData),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> uploadPaymentReceipt(String token, String invoiceId, String imagePath) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/invoices/$invoiceId/payment-receipt'),
      );
      
      request.headers.addAll(_headersWithAuth(token));
      request.files.add(await http.MultipartFile.fromPath('receipt', imagePath));

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // File upload
  Future<Map<String, dynamic>> uploadImage(String token, String imagePath) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/upload'),
      );
      
      request.headers.addAll(_headersWithAuth(token));
      request.files.add(await http.MultipartFile.fromPath('image', imagePath));

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Map<String, dynamic> _handleResponse(http.Response response) {
    final Map<String, dynamic> data = jsonDecode(response.body);
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return data;
    } else {
      throw Exception(data['message'] ?? 'Unknown error occurred');
    }
  }
}
