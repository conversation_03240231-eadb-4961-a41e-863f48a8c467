^C:\USERS\<USER>\DESKTOP\CARSPART\WINDOWS\RUNNER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Desktop/carspart/windows" "-BC:/Users/<USER>/Desktop/carspart/build/windows/x64" --check-stamp-file "C:/Users/<USER>/Desktop/carspart/build/windows/x64/runner/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
