class Order {
  final String id;
  final String customerId;
  final String customerName;
  final String title;
  final String description;
  final String carModel;
  final String carYear;
  final List<String> images;
  final OrderStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<Offer> offers;
  final List<Message> messages;
  final Invoice? invoice;

  Order({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.title,
    required this.description,
    required this.carModel,
    required this.carYear,
    required this.images,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.offers = const [],
    this.messages = const [],
    this.invoice,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      customerId: json['customer_id'],
      customerName: json['customer_name'],
      title: json['title'],
      description: json['description'],
      carModel: json['car_model'],
      carYear: json['car_year'],
      images: List<String>.from(json['images'] ?? []),
      status: OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      offers: (json['offers'] as List?)?.map((e) => Offer.fromJson(e)).toList() ?? [],
      messages: (json['messages'] as List?)?.map((e) => Message.fromJson(e)).toList() ?? [],
      invoice: json['invoice'] != null ? Invoice.fromJson(json['invoice']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'title': title,
      'description': description,
      'car_model': carModel,
      'car_year': carYear,
      'images': images,
      'status': status.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'offers': offers.map((e) => e.toJson()).toList(),
      'messages': messages.map((e) => e.toJson()).toList(),
      'invoice': invoice?.toJson(),
    };
  }
}

enum OrderStatus {
  pending,      // في انتظار العروض
  hasOffers,    // يوجد عروض
  negotiating,  // في مرحلة التفاوض
  agreed,       // تم الاتفاق
  invoiced,     // تم إنشاء الفاتورة
  paid,         // تم الدفع
  completed,    // مكتمل
  cancelled     // ملغي
}

extension OrderStatusExtension on OrderStatus {
  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'في انتظار العروض';
      case OrderStatus.hasOffers:
        return 'يوجد عروض';
      case OrderStatus.negotiating:
        return 'في مرحلة التفاوض';
      case OrderStatus.agreed:
        return 'تم الاتفاق';
      case OrderStatus.invoiced:
        return 'تم إنشاء الفاتورة';
      case OrderStatus.paid:
        return 'تم الدفع';
      case OrderStatus.completed:
        return 'مكتمل';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }
}

class Offer {
  final String id;
  final String orderId;
  final String shopId;
  final String shopName;
  final double price;
  final String description;
  final List<String> images;
  final int deliveryDays;
  final bool isSelected;
  final DateTime createdAt;

  Offer({
    required this.id,
    required this.orderId,
    required this.shopId,
    required this.shopName,
    required this.price,
    required this.description,
    required this.images,
    required this.deliveryDays,
    this.isSelected = false,
    required this.createdAt,
  });

  factory Offer.fromJson(Map<String, dynamic> json) {
    return Offer(
      id: json['id'],
      orderId: json['order_id'],
      shopId: json['shop_id'],
      shopName: json['shop_name'],
      price: double.parse(json['price'].toString()),
      description: json['description'],
      images: List<String>.from(json['images'] ?? []),
      deliveryDays: json['delivery_days'],
      isSelected: json['is_selected'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'shop_id': shopId,
      'shop_name': shopName,
      'price': price,
      'description': description,
      'images': images,
      'delivery_days': deliveryDays,
      'is_selected': isSelected,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class Message {
  final String id;
  final String orderId;
  final String senderId;
  final String senderName;
  final String senderType;
  final String content;
  final List<String> images;
  final DateTime createdAt;

  Message({
    required this.id,
    required this.orderId,
    required this.senderId,
    required this.senderName,
    required this.senderType,
    required this.content,
    this.images = const [],
    required this.createdAt,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      orderId: json['order_id'],
      senderId: json['sender_id'],
      senderName: json['sender_name'],
      senderType: json['sender_type'],
      content: json['content'],
      images: List<String>.from(json['images'] ?? []),
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'sender_id': senderId,
      'sender_name': senderName,
      'sender_type': senderType,
      'content': content,
      'images': images,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class Invoice {
  final String id;
  final String orderId;
  final String offerId;
  final double originalPrice;
  final double commission;
  final double totalPrice;
  final String bankDetails;
  final InvoiceStatus status;
  final DateTime createdAt;
  final String? paymentReceiptImage;

  Invoice({
    required this.id,
    required this.orderId,
    required this.offerId,
    required this.originalPrice,
    required this.commission,
    required this.totalPrice,
    required this.bankDetails,
    required this.status,
    required this.createdAt,
    this.paymentReceiptImage,
  });

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: json['id'],
      orderId: json['order_id'],
      offerId: json['offer_id'],
      originalPrice: double.parse(json['original_price'].toString()),
      commission: double.parse(json['commission'].toString()),
      totalPrice: double.parse(json['total_price'].toString()),
      bankDetails: json['bank_details'],
      status: InvoiceStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      createdAt: DateTime.parse(json['created_at']),
      paymentReceiptImage: json['payment_receipt_image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'offer_id': offerId,
      'original_price': originalPrice,
      'commission': commission,
      'total_price': totalPrice,
      'bank_details': bankDetails,
      'status': status.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'payment_receipt_image': paymentReceiptImage,
    };
  }
}

enum InvoiceStatus {
  pending,    // في انتظار الدفع
  paid,       // تم الدفع
  confirmed   // تم التأكيد
}

extension InvoiceStatusExtension on InvoiceStatus {
  String get displayName {
    switch (this) {
      case InvoiceStatus.pending:
        return 'في انتظار الدفع';
      case InvoiceStatus.paid:
        return 'تم الدفع';
      case InvoiceStatus.confirmed:
        return 'تم التأكيد';
    }
  }
}
