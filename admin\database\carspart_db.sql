-- قاعدة بيانات تطبيق قطع غيار السيارات
-- Cars Parts Database

CREATE DATABASE IF NOT EXISTS carspart_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE carspart_db;

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('customer', 'shop', 'mediator') NOT NULL,
    profile_image VARCHAR(255) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الطلبات
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    car_model VARCHAR(100) NOT NULL,
    car_year VARCHAR(4) NOT NULL,
    images JSON NULL,
    status ENUM('pending', 'has_offers', 'negotiating', 'agreed', 'invoiced', 'paid', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول العروض
CREATE TABLE offers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    shop_id INT NOT NULL,
    price DECIMAL(10,3) NOT NULL,
    description TEXT NOT NULL,
    images JSON NULL,
    delivery_days INT NOT NULL,
    is_selected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (shop_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الرسائل
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    sender_id INT NOT NULL,
    content TEXT NOT NULL,
    images JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الفواتير
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    offer_id INT NOT NULL,
    original_price DECIMAL(10,3) NOT NULL,
    commission DECIMAL(10,3) NOT NULL,
    commission_percentage DECIMAL(5,2) DEFAULT 10.00,
    total_price DECIMAL(10,3) NOT NULL,
    bank_details TEXT NOT NULL,
    status ENUM('pending', 'paid', 'confirmed') DEFAULT 'pending',
    payment_receipt_image VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الملفات المرفوعة
CREATE TABLE uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INT NOT NULL,
    related_type ENUM('order', 'offer', 'message', 'invoice') NOT NULL,
    related_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إدراج بيانات تجريبية

-- إدراج المستخدمين (كلمة المرور: password)
INSERT INTO users (name, email, phone, password, user_type) VALUES
('أحمد محمد', '<EMAIL>', '96812345678', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'customer'),
('فاطمة علي', '<EMAIL>', '96887654321', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'customer'),
('محل الشارقة لقطع الغيار', '<EMAIL>', '97150123456', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'shop'),
('محل دبي للسيارات', '<EMAIL>', '97150654321', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'shop'),
('الوسيط الرئيسي', '<EMAIL>', '***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mediator');

-- إدراج طلبات تجريبية
INSERT INTO orders (customer_id, title, description, car_model, car_year, status) VALUES
(1, 'مصباح أمامي لتويوتا كامري', 'أحتاج مصباح أمامي أيمن لتويوتا كامري موديل 2018، يفضل أن يكون أصلي من الوكيل', 'تويوتا كامري', '2018', 'has_offers'),
(2, 'فرامل لنيسان التيما', 'أحتاج طقم فرامل كامل لنيسان التيما 2020، أمامي وخلفي', 'نيسان التيما', '2020', 'pending'),
(1, 'مرآة جانبية لهوندا أكورد', 'مرآة جانبية يسار لهوندا أكورد 2019، مكسورة بسبب حادث', 'هوندا أكورد', '2019', 'negotiating');

-- إدراج عروض تجريبية
INSERT INTO offers (order_id, shop_id, price, description, delivery_days) VALUES
(1, 3, 85.500, 'مصباح أمامي أصلي من الوكيل، جديد بالكرتون، ضمان سنة كاملة', 3),
(1, 4, 75.000, 'مصباح أمامي تجاري جودة عالية، ضمان 6 شهور', 2),
(3, 3, 45.000, 'مرآة جانبية أصلية، لون أبيض، جديدة', 4);

-- إدراج رسائل تجريبية
INSERT INTO messages (order_id, sender_id, content) VALUES
(1, 5, 'مرحباً أحمد، لديك عرضان ممتازان لمصباح تويوتا كامري. أفضل سعر هو 75 ريال عماني من محل دبي للسيارات'),
(1, 1, 'شكراً لك، هل يمكنني رؤية صور للمصباح؟'),
(3, 5, 'فاطمة، يوجد عرض واحد للمرآة الجانبية بسعر 45 ريال عماني، هل توافقين؟');

-- إدراج فاتورة تجريبية
INSERT INTO invoices (order_id, offer_id, original_price, commission, total_price, bank_details, status) VALUES
(1, 2, 75.000, 7.500, 82.500, 'البنك الأهلي العماني - رقم الحساب: **********', 'pending');

-- إدراج الإعدادات الافتراضية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('commission_percentage', '10', 'نسبة العمولة الافتراضية'),
('bank_details', 'البنك الأهلي العماني\nرقم الحساب: **********\nاسم الحساب: شركة قطع غيار السيارات', 'تفاصيل الحساب البنكي'),
('app_name', 'قطع غيار السيارات', 'اسم التطبيق'),
('contact_phone', '***********', 'رقم الهاتف للتواصل'),
('contact_email', '<EMAIL>', 'البريد الإلكتروني للتواصل');

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_offers_order_id ON offers(order_id);
CREATE INDEX idx_offers_shop_id ON offers(shop_id);
CREATE INDEX idx_messages_order_id ON messages(order_id);
CREATE INDEX idx_invoices_order_id ON invoices(order_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_user_type ON users(user_type);

-- إنشاء views مفيدة
CREATE VIEW order_details AS
SELECT 
    o.id,
    o.title,
    o.description,
    o.car_model,
    o.car_year,
    o.status,
    o.created_at,
    u.name as customer_name,
    u.email as customer_email,
    u.phone as customer_phone,
    COUNT(of.id) as offers_count,
    MIN(of.price) as min_price,
    MAX(of.price) as max_price
FROM orders o
LEFT JOIN users u ON o.customer_id = u.id
LEFT JOIN offers of ON o.id = of.order_id
GROUP BY o.id;

CREATE VIEW shop_statistics AS
SELECT 
    u.id,
    u.name as shop_name,
    COUNT(of.id) as total_offers,
    COUNT(CASE WHEN of.is_selected = 1 THEN 1 END) as selected_offers,
    AVG(of.price) as avg_price,
    MIN(of.price) as min_price,
    MAX(of.price) as max_price
FROM users u
LEFT JOIN offers of ON u.id = of.shop_id
WHERE u.user_type = 'shop'
GROUP BY u.id;
