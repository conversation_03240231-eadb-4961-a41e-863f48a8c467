/* لوحة تحكم قطع غيار السيارات */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 2px 10px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.15);
    font-weight: 600;
}

.sidebar .nav-link i {
    margin-left: 8px;
    width: 16px;
    text-align: center;
}

/* البطاقات الإحصائية */
.border-left-primary {
    border-right: 0.25rem solid #007bff !important;
    border-left: none !important;
}

.border-left-success {
    border-right: 0.25rem solid #28a745 !important;
    border-left: none !important;
}

.border-left-info {
    border-right: 0.25rem solid #17a2b8 !important;
    border-left: none !important;
}

.border-left-warning {
    border-right: 0.25rem solid #ffc107 !important;
    border-left: none !important;
}

.border-left-danger {
    border-right: 0.25rem solid #dc3545 !important;
    border-left: none !important;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 12px 12px 0 0 !important;
    padding: 1rem 1.25rem;
}

/* الجداول */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fc;
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #0f8a7c 0%, #32d96b 100%);
}

/* الشارات */
.badge {
    font-weight: 500;
    border-radius: 6px;
    padding: 0.4em 0.6em;
}

/* النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e3e6f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.25rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* الشريط العلوي */
.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-brand i {
    margin-left: 8px;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }
    
    main {
        margin-right: 0 !important;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسينات إضافية */
.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.h-100 {
    height: 100% !important;
}

.py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

/* تخصيص الألوان */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #11998e;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
}

/* تحسين المظهر العام */
.container-fluid {
    padding-right: 0;
    padding-left: 0;
}

main {
    margin-right: 16.66667%;
    padding: 2rem;
}

/* تحسين الجداول للشاشات الصغيرة */
@media (max-width: 992px) {
    .table-responsive {
        border: none;
    }
    
    .table-responsive .table {
        margin-bottom: 0;
    }
}

/* تحسين النصوص العربية */
.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

/* تحسين الأيقونات */
.fas, .far, .fab {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
}

/* تحسين المسافات */
.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mt-4 {
    margin-top: 1.5rem !important;
}

.p-4 {
    padding: 1.5rem !important;
}

/* تحسين الحدود */
.border-bottom {
    border-bottom: 1px solid #e3e6f0 !important;
}

.border-top {
    border-top: 1px solid #e3e6f0 !important;
}

/* تحسين الخلفيات */
.bg-light {
    background-color: #f8f9fc !important;
}

.bg-white {
    background-color: #ffffff !important;
}

/* تحسين الظلال */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}
