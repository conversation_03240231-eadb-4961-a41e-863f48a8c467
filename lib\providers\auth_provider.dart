import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _error;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _currentUser != null;

  AuthProvider() {
    _loadUserFromStorage();
  }

  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('user');
      if (userJson != null) {
        // In a real app, you would parse the JSON and create a User object
        // For now, we'll just set a flag that user is logged in
      }
    } catch (e) {
      print('Error loading user from storage: $e');
    }
  }

  Future<bool> login(String email, String password, UserType userType) async {
    _setLoading(true);
    _error = null;

    try {
      final apiService = ApiService();
      final response = await apiService.login(email, password, userType.toString().split('.').last);

      if (response['success'] == true) {
        final userData = response['data']['user'];
        _currentUser = User.fromJson(userData);

        await _saveUserToStorage();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'حدث خطأ أثناء تسجيل الدخول';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'حدث خطأ في الاتصال بالخادم';
      _setLoading(false);
      return false;
    }
  }

  Future<bool> register(String name, String email, String password, String phone, UserType userType) async {
    _setLoading(true);
    _error = null;

    try {
      final apiService = ApiService();
      final response = await apiService.register({
        'name': name,
        'email': email,
        'password': password,
        'phone': phone,
        'user_type': userType.toString().split('.').last,
      });

      if (response['success'] == true) {
        final userData = response['data']['user'];
        _currentUser = User.fromJson(userData);

        await _saveUserToStorage();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'حدث خطأ أثناء التسجيل';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'حدث خطأ في الاتصال بالخادم';
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user');
    notifyListeners();
  }

  Future<void> _saveUserToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user', _currentUser!.toJson().toString());
    } catch (e) {
      print('Error saving user to storage: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }



  void clearError() {
    _error = null;
    notifyListeners();
  }
}
