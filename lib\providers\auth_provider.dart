import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _error;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _currentUser != null;

  AuthProvider() {
    _loadUserFromStorage();
  }

  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('user');
      if (userJson != null) {
        // In a real app, you would parse the JSON and create a User object
        // For now, we'll just set a flag that user is logged in
      }
    } catch (e) {
      print('Error loading user from storage: $e');
    }
  }

  Future<bool> login(String email, String password, UserType userType) async {
    _setLoading(true);
    _error = null;

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // For demo purposes, accept any email/password
      if (email.isNotEmpty && password.isNotEmpty) {
        _currentUser = User(
          id: '1',
          name: _getNameFromEmail(email),
          email: email,
          phone: '96812345678',
          userType: userType,
          createdAt: DateTime.now(),
        );
        
        await _saveUserToStorage();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _error = 'البريد الإلكتروني وكلمة المرور مطلوبان';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'حدث خطأ أثناء تسجيل الدخول';
      _setLoading(false);
      return false;
    }
  }

  Future<bool> register(String name, String email, String password, String phone, UserType userType) async {
    _setLoading(true);
    _error = null;

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      if (name.isNotEmpty && email.isNotEmpty && password.isNotEmpty && phone.isNotEmpty) {
        _currentUser = User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          email: email,
          phone: phone,
          userType: userType,
          createdAt: DateTime.now(),
        );
        
        await _saveUserToStorage();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _error = 'جميع الحقول مطلوبة';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'حدث خطأ أثناء التسجيل';
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user');
    notifyListeners();
  }

  Future<void> _saveUserToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user', _currentUser!.toJson().toString());
    } catch (e) {
      print('Error saving user to storage: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  String _getNameFromEmail(String email) {
    return email.split('@')[0];
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
