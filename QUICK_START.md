# 🚀 دليل التشغيل السريع - نظام قطع غيار السيارات

## 📋 المتطلبات
- PHP 7.4+ مع PDO
- MySQL 5.7+
- Flutter SDK
- محرر أكواد (VS Code مُفضل)

## ⚡ التشغيل السريع

### 1️⃣ إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE carspart_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد البيانات
mysql -u root -p carspart_db < admin/database/carspart_db.sql
```

### 2️⃣ تشغيل الخادم PHP
```bash
# الطريقة الأولى: استخدام الملف المساعد
cd admin
start_server.bat

# الطريقة الثانية: يدوياً
cd admin
php -S localhost:8000
```

### 3️⃣ تشغيل تطبيق Flutter
```bash
cd carspart
flutter pub get
flutter run
```

## 🔑 بيانات تسجيل الدخول

### لوحة التحكم PHP
- **الرابط:** http://localhost:8000/admin/
- **البريد:** <EMAIL>
- **كلمة المرور:** password

### تطبيق Flutter
| النوع | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| عميل | <EMAIL> | password |
| محل | <EMAIL> | password |
| وسيط | <EMAIL> | password |

## 🌐 روابط مهمة

- **لوحة التحكم:** http://localhost:8000/admin/
- **API Base:** http://localhost:8000/admin/api/
- **قاعدة البيانات:** localhost:3306/carspart_db

## 🔧 إعدادات API في Flutter

في ملف `lib/services/api_service.dart`:
```dart
static const String baseUrl = 'http://localhost:8000/admin/api';
```

للاختبار على الهاتف الحقيقي، غير localhost إلى IP الجهاز:
```dart
static const String baseUrl = 'http://*************:8000/admin/api';
```

## 📱 اختبار النظام

### 1. اختبار العميل
1. سجل دخول كعميل في التطبيق
2. أنشئ طلب قطعة غيار جديد
3. أضف صور ووصف مفصل

### 2. اختبار المحل
1. سجل دخول كمحل في التطبيق
2. شاهد الطلبات المتاحة
3. قدم عرض بسعر وتفاصيل

### 3. اختبار الوسيط
1. ادخل لوحة التحكم PHP
2. شاهد الطلبات والعروض
3. تواصل مع العميل
4. أنشئ فاتورة للعرض المختار

## 🐛 حل المشاكل الشائعة

### خطأ في الاتصال بقاعدة البيانات
```php
// تحقق من إعدادات admin/config/database.php
private $host = 'localhost';
private $db_name = 'carspart_db';
private $username = 'root';
private $password = '';
```

### خطأ CORS في Flutter
```dart
// تأكد من أن الخادم يعمل على localhost:8000
// وأن API endpoints تستجيب بشكل صحيح
```

### خطأ في رفع الملفات
```bash
# تأكد من وجود مجلد uploads وصلاحياته
mkdir admin/uploads
chmod 755 admin/uploads
```

## 📊 اختبار API مباشرة

### تسجيل الدخول
```bash
curl -X POST http://localhost:8000/admin/api/auth.php?action=login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password","user_type":"mediator"}'
```

### جلب الطلبات
```bash
curl "http://localhost:8000/admin/api/orders.php?user_type=mediator&user_id=5"
```

## 🎯 الخطوات التالية

1. **اختبر جميع الوظائف** في التطبيق ولوحة التحكم
2. **راجع البيانات** في قاعدة البيانات
3. **جرب سيناريوهات مختلفة** للطلبات والعروض
4. **تحقق من الرسائل** والتواصل بين الأطراف

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من logs الخادم
2. راجع console المتصفح للأخطاء
3. تأكد من تشغيل جميع الخدمات
4. راجع إعدادات قاعدة البيانات

---

**🎉 مبروك! النظام جاهز للاستخدام**
