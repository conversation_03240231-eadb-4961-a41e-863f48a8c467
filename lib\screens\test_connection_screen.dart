import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class TestConnectionScreen extends StatefulWidget {
  const TestConnectionScreen({super.key});

  @override
  State<TestConnectionScreen> createState() => _TestConnectionScreenState();
}

class _TestConnectionScreenState extends State<TestConnectionScreen> {
  String _serverStatus = 'غير محدد';
  String _dbStatus = 'غير محدد';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _testConnections();
  }

  Future<void> _testConnections() async {
    setState(() {
      _isLoading = true;
    });

    await _testServer();
    await _testDatabase();

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testServer() async {
    try {
      final response = await http.get(
        Uri.parse('http://localhost:8000/test.php'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _serverStatus = data['success'] ? 'متصل ✅' : 'خطأ ❌';
        });
      } else {
        setState(() {
          _serverStatus = 'خطأ HTTP ${response.statusCode} ❌';
        });
      }
    } catch (e) {
      setState(() {
        _serverStatus = 'فشل الاتصال ❌\n$e';
      });
    }
  }

  Future<void> _testDatabase() async {
    try {
      final response = await http.get(
        Uri.parse('http://localhost:8000/test_db.php'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _dbStatus = data['success'] 
              ? 'متصل ✅\nعدد المستخدمين: ${data['data']['users_count']}'
              : 'خطأ ❌\n${data['message']}';
        });
      } else {
        setState(() {
          _dbStatus = 'خطأ HTTP ${response.statusCode} ❌';
        });
      }
    } catch (e) {
      setState(() {
        _dbStatus = 'فشل الاتصال ❌\n$e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الاتصال'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'حالة الخادم PHP',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _serverStatus,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'حالة قاعدة البيانات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _dbStatus,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _isLoading ? null : _testConnections,
              child: _isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('جاري الاختبار...'),
                      ],
                    )
                  : const Text('إعادة اختبار الاتصال'),
            ),
            const SizedBox(height: 16),
            const Card(
              color: Colors.blue,
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تعليمات:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '1. تأكد من تشغيل خادم PHP على localhost:8000\n'
                      '2. تأكد من إنشاء قاعدة البيانات carspart_db\n'
                      '3. تأكد من استيراد ملف SQL\n'
                      '4. تحقق من إعدادات قاعدة البيانات في config/database.php',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
