<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];
$request = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            handleGetOffers($db);
            break;
            
        case 'POST':
            handleCreateOffer($db, $request);
            break;
            
        default:
            errorResponse('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    errorResponse('Server error: ' . $e->getMessage(), 500);
}

function handleGetOffers($db) {
    $orderId = $_GET['order_id'] ?? '';
    
    if (empty($orderId)) {
        errorResponse('معرف الطلب مطلوب');
    }
    
    $offers = $db->fetchAll("
        SELECT of.*, u.name as shop_name, u.email as shop_email, u.phone as shop_phone
        FROM offers of
        LEFT JOIN users u ON of.shop_id = u.id
        WHERE of.order_id = ?
        ORDER BY of.price ASC
    ", [$orderId]);
    
    // تحويل JSON إلى array
    foreach ($offers as &$offer) {
        if ($offer['images']) {
            $offer['images'] = json_decode($offer['images'], true) ?: [];
        } else {
            $offer['images'] = [];
        }
    }
    
    successResponse($offers);
}

function handleCreateOffer($db, $request) {
    $orderId = $request['order_id'] ?? '';
    $shopId = $request['shop_id'] ?? '';
    $shopName = sanitize($request['shop_name'] ?? '');
    $price = floatval($request['price'] ?? 0);
    $description = sanitize($request['description'] ?? '');
    $deliveryDays = intval($request['delivery_days'] ?? 0);
    $images = $request['images'] ?? [];
    
    if (empty($orderId) || empty($shopId) || $price <= 0 || empty($description) || $deliveryDays <= 0) {
        errorResponse('جميع الحقول مطلوبة والقيم يجب أن تكون صحيحة');
    }
    
    // التحقق من وجود الطلب
    $order = $db->fetch("SELECT id, status FROM orders WHERE id = ?", [$orderId]);
    if (!$order) {
        errorResponse('الطلب غير موجود');
    }
    
    // التحقق من وجود المحل
    $shop = $db->fetch("SELECT id, name FROM users WHERE id = ? AND user_type = 'shop'", [$shopId]);
    if (!$shop) {
        errorResponse('المحل غير موجود');
    }
    
    // التحقق من عدم وجود عرض مسبق من نفس المحل لنفس الطلب
    $existingOffer = $db->fetch("SELECT id FROM offers WHERE order_id = ? AND shop_id = ?", [$orderId, $shopId]);
    if ($existingOffer) {
        errorResponse('لقد قدمت عرضاً مسبقاً لهذا الطلب');
    }
    
    try {
        $db->beginTransaction();
        
        // إنشاء العرض
        $offerId = $db->insert('offers', [
            'order_id' => $orderId,
            'shop_id' => $shopId,
            'price' => $price,
            'description' => $description,
            'images' => json_encode($images),
            'delivery_days' => $deliveryDays
        ]);
        
        // تحديث حالة الطلب إلى "لديه عروض"
        $db->update('orders', ['status' => 'has_offers'], 'id = ?', [$orderId]);
        
        $db->commit();
        
        // جلب العرض الجديد
        $offer = $db->fetch("
            SELECT of.*, u.name as shop_name, u.email as shop_email, u.phone as shop_phone
            FROM offers of
            LEFT JOIN users u ON of.shop_id = u.id
            WHERE of.id = ?
        ", [$offerId]);
        
        $offer['images'] = json_decode($offer['images'], true) ?: [];
        
        successResponse($offer, 'تم إرسال العرض بنجاح');
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}
?>
