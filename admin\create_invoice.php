<?php
session_start();
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'mediator') {
    header('Location: login.php');
    exit;
}

$order_id = $_GET['order_id'] ?? 0;
$offer_id = $_GET['offer_id'] ?? 0;

if (!$order_id) {
    header('Location: orders.php');
    exit;
}

$db = getDB();

// الحصول على تفاصيل الطلب
$order = $db->fetch("
    SELECT o.*, u.name as customer_name, u.email as customer_email, u.phone as customer_phone
    FROM orders o 
    LEFT JOIN users u ON o.customer_id = u.id 
    WHERE o.id = ?
", [$order_id]);

if (!$order) {
    header('Location: orders.php');
    exit;
}

// الحصول على العرض المحدد أو أفضل عرض
if ($offer_id) {
    $selectedOffer = $db->fetch("
        SELECT of.*, u.name as shop_name, u.email as shop_email, u.phone as shop_phone
        FROM offers of
        LEFT JOIN users u ON of.shop_id = u.id
        WHERE of.id = ? AND of.order_id = ?
    ", [$offer_id, $order_id]);
} else {
    $selectedOffer = $db->fetch("
        SELECT of.*, u.name as shop_name, u.email as shop_email, u.phone as shop_phone
        FROM offers of
        LEFT JOIN users u ON of.shop_id = u.id
        WHERE of.order_id = ?
        ORDER BY of.price ASC
        LIMIT 1
    ", [$order_id]);
}

if (!$selectedOffer) {
    header('Location: order_details.php?id=' . $order_id);
    exit;
}

// الحصول على الإعدادات
$commissionPercentage = $db->fetch("SELECT setting_value FROM settings WHERE setting_key = 'commission_percentage'")['setting_value'] ?? 10;
$bankDetails = $db->fetch("SELECT setting_value FROM settings WHERE setting_key = 'bank_details'")['setting_value'] ?? '';

$error = '';
$success = '';

// معالجة إنشاء الفاتورة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $originalPrice = floatval($_POST['original_price'] ?? 0);
    $commissionPercentage = floatval($_POST['commission_percentage'] ?? 10);
    $bankDetails = sanitize($_POST['bank_details'] ?? '');
    
    if ($originalPrice <= 0) {
        $error = 'السعر الأصلي يجب أن يكون أكبر من صفر';
    } elseif ($commissionPercentage < 0 || $commissionPercentage > 100) {
        $error = 'نسبة العمولة يجب أن تكون بين 0 و 100';
    } elseif (empty($bankDetails)) {
        $error = 'تفاصيل الحساب البنكي مطلوبة';
    } else {
        // التحقق من عدم وجود فاتورة مسبقة لهذا الطلب
        $existingInvoice = $db->fetch("SELECT id FROM invoices WHERE order_id = ?", [$order_id]);
        
        if ($existingInvoice) {
            $error = 'يوجد فاتورة مسبقة لهذا الطلب';
        } else {
            try {
                $db->beginTransaction();
                
                // حساب العمولة والسعر الإجمالي
                $commission = ($originalPrice * $commissionPercentage) / 100;
                $totalPrice = $originalPrice + $commission;
                
                // إنشاء الفاتورة
                $invoiceId = $db->insert('invoices', [
                    'order_id' => $order_id,
                    'offer_id' => $selectedOffer['id'],
                    'original_price' => $originalPrice,
                    'commission' => $commission,
                    'commission_percentage' => $commissionPercentage,
                    'total_price' => $totalPrice,
                    'bank_details' => $bankDetails,
                    'status' => 'pending'
                ]);
                
                // تحديث حالة الطلب
                $db->update('orders', ['status' => 'invoiced'], 'id = ?', [$order_id]);
                
                // تحديث العرض المختار
                $db->update('offers', ['is_selected' => 1], 'id = ?', [$selectedOffer['id']]);
                
                // إرسال رسالة للعميل
                $messageContent = "تم إنشاء فاتورة لطلبك بمبلغ " . formatPrice($totalPrice) . "\n\n";
                $messageContent .= "تفاصيل الفاتورة:\n";
                $messageContent .= "- سعر القطعة: " . formatPrice($originalPrice) . "\n";
                $messageContent .= "- عمولة الوساطة ({$commissionPercentage}%): " . formatPrice($commission) . "\n";
                $messageContent .= "- المبلغ الإجمالي: " . formatPrice($totalPrice) . "\n\n";
                $messageContent .= "تفاصيل التحويل:\n" . $bankDetails;
                
                $db->insert('messages', [
                    'order_id' => $order_id,
                    'sender_id' => $_SESSION['user_id'],
                    'content' => $messageContent
                ]);
                
                $db->commit();
                $success = 'تم إنشاء الفاتورة بنجاح';
                
                // إعادة توجيه بعد 2 ثانية
                header("refresh:2;url=invoice_details.php?id=$invoiceId");
                
            } catch (Exception $e) {
                $db->rollback();
                $error = 'حدث خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء فاتورة - الطلب #<?php echo $order['id']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-car"></i> قطع غيار السيارات
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo $_SESSION['user_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="orders.php">
                                <i class="fas fa-shopping-cart"></i> الطلبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="invoices.php">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إنشاء فاتورة جديدة</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="order_details.php?id=<?php echo $order_id; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للطلب
                        </a>
                    </div>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success; ?>
                        <div class="mt-2">
                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                            جاري إعادة التوجيه لصفحة الفاتورة...
                        </div>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-8">
                        <!-- Order Summary -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">ملخص الطلب</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>معلومات الطلب:</h6>
                                        <p><strong>رقم الطلب:</strong> #<?php echo $order['id']; ?></p>
                                        <p><strong>العنوان:</strong> <?php echo htmlspecialchars($order['title']); ?></p>
                                        <p><strong>السيارة:</strong> <?php echo htmlspecialchars($order['car_model'] . ' - ' . $order['car_year']); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>معلومات العميل:</h6>
                                        <p><strong>الاسم:</strong> <?php echo htmlspecialchars($order['customer_name']); ?></p>
                                        <p><strong>الهاتف:</strong> <?php echo htmlspecialchars($order['customer_phone']); ?></p>
                                        <p><strong>البريد:</strong> <?php echo htmlspecialchars($order['customer_email']); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Selected Offer -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">العرض المختار</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6><?php echo htmlspecialchars($selectedOffer['shop_name']); ?></h6>
                                        <p><?php echo nl2br(htmlspecialchars($selectedOffer['description'])); ?></p>
                                        <p><strong>مدة التوصيل:</strong> <?php echo $selectedOffer['delivery_days']; ?> أيام</p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <h4 class="text-success"><?php echo formatPrice($selectedOffer['price']); ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Form -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">تفاصيل الفاتورة</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="original_price" class="form-label">السعر الأصلي (ريال عماني)</label>
                                                <input type="number" step="0.001" class="form-control" id="original_price" 
                                                       name="original_price" value="<?php echo $selectedOffer['price']; ?>" 
                                                       required onchange="calculateTotal()">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="commission_percentage" class="form-label">نسبة العمولة (%)</label>
                                                <input type="number" step="0.01" class="form-control" id="commission_percentage" 
                                                       name="commission_percentage" value="<?php echo $commissionPercentage; ?>" 
                                                       min="0" max="100" required onchange="calculateTotal()">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">العمولة (ريال عماني)</label>
                                                <input type="text" class="form-control" id="commission_display" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">المبلغ الإجمالي (ريال عماني)</label>
                                                <input type="text" class="form-control" id="total_price_display" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="bank_details" class="form-label">تفاصيل الحساب البنكي</label>
                                        <textarea class="form-control" id="bank_details" name="bank_details" 
                                                  rows="4" required><?php echo htmlspecialchars($bankDetails); ?></textarea>
                                        <div class="form-text">سيتم إرسال هذه التفاصيل للعميل مع الفاتورة</div>
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-file-invoice"></i> إنشاء الفاتورة
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Invoice Preview -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">معاينة الفاتورة</h6>
                            </div>
                            <div class="card-body">
                                <div class="invoice-preview">
                                    <h6 class="text-center mb-3">فاتورة قطع غيار السيارات</h6>
                                    
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>رقم الطلب:</strong></td>
                                            <td>#<?php echo $order['id']; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>العميل:</strong></td>
                                            <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>القطعة:</strong></td>
                                            <td><?php echo htmlspecialchars($order['title']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>المحل:</strong></td>
                                            <td><?php echo htmlspecialchars($selectedOffer['shop_name']); ?></td>
                                        </tr>
                                    </table>

                                    <hr>

                                    <table class="table table-sm">
                                        <tr>
                                            <td>سعر القطعة:</td>
                                            <td class="text-end" id="preview_original">-</td>
                                        </tr>
                                        <tr>
                                            <td>عمولة الوساطة:</td>
                                            <td class="text-end" id="preview_commission">-</td>
                                        </tr>
                                        <tr class="table-success">
                                            <td><strong>المبلغ الإجمالي:</strong></td>
                                            <td class="text-end"><strong id="preview_total">-</strong></td>
                                        </tr>
                                    </table>

                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <strong>ملاحظة:</strong> يرجى تحويل المبلغ إلى الحساب المذكور وإرسال إيصال التحويل.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function calculateTotal() {
            const originalPrice = parseFloat(document.getElementById('original_price').value) || 0;
            const commissionPercentage = parseFloat(document.getElementById('commission_percentage').value) || 0;
            
            const commission = (originalPrice * commissionPercentage) / 100;
            const totalPrice = originalPrice + commission;
            
            // Update form fields
            document.getElementById('commission_display').value = commission.toFixed(3);
            document.getElementById('total_price_display').value = totalPrice.toFixed(3);
            
            // Update preview
            document.getElementById('preview_original').textContent = originalPrice.toFixed(3) + ' ر.ع';
            document.getElementById('preview_commission').textContent = commission.toFixed(3) + ' ر.ع (' + commissionPercentage + '%)';
            document.getElementById('preview_total').textContent = totalPrice.toFixed(3) + ' ر.ع';
        }
        
        // Calculate on page load
        calculateTotal();
    </script>
</body>
</html>
