<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];
$request = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            handleGetInvoices($db);
            break;
            
        case 'POST':
            handleCreateInvoice($db, $request);
            break;
            
        case 'PUT':
            handleUpdateInvoice($db, $request);
            break;
            
        default:
            errorResponse('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    errorResponse('Server error: ' . $e->getMessage(), 500);
}

function handleGetInvoices($db) {
    $orderId = $_GET['order_id'] ?? '';
    $customerId = $_GET['customer_id'] ?? '';
    
    $whereClause = '';
    $params = [];
    
    if (!empty($orderId)) {
        $whereClause = 'WHERE i.order_id = ?';
        $params = [$orderId];
    } elseif (!empty($customerId)) {
        $whereClause = 'WHERE o.customer_id = ?';
        $params = [$customerId];
    }
    
    $invoices = $db->fetchAll("
        SELECT 
            i.*,
            o.title as order_title,
            o.customer_id,
            u.name as customer_name,
            u.email as customer_email,
            u.phone as customer_phone,
            of.shop_id,
            s.name as shop_name
        FROM invoices i
        LEFT JOIN orders o ON i.order_id = o.id
        LEFT JOIN users u ON o.customer_id = u.id
        LEFT JOIN offers of ON i.offer_id = of.id
        LEFT JOIN users s ON of.shop_id = s.id
        {$whereClause}
        ORDER BY i.created_at DESC
    ", $params);
    
    successResponse($invoices);
}

function handleCreateInvoice($db, $request) {
    $orderId = $request['order_id'] ?? '';
    $offerId = $request['offer_id'] ?? '';
    $originalPrice = floatval($request['original_price'] ?? 0);
    $commissionPercentage = floatval($request['commission_percentage'] ?? 10);
    $bankDetails = sanitize($request['bank_details'] ?? '');
    
    if (empty($orderId) || empty($offerId) || $originalPrice <= 0 || empty($bankDetails)) {
        errorResponse('جميع الحقول مطلوبة والقيم يجب أن تكون صحيحة');
    }
    
    // التحقق من وجود الطلب والعرض
    $order = $db->fetch("SELECT id, status FROM orders WHERE id = ?", [$orderId]);
    if (!$order) {
        errorResponse('الطلب غير موجود');
    }
    
    $offer = $db->fetch("SELECT id, price FROM offers WHERE id = ? AND order_id = ?", [$offerId, $orderId]);
    if (!$offer) {
        errorResponse('العرض غير موجود');
    }
    
    // التحقق من عدم وجود فاتورة مسبقة
    $existingInvoice = $db->fetch("SELECT id FROM invoices WHERE order_id = ?", [$orderId]);
    if ($existingInvoice) {
        errorResponse('يوجد فاتورة مسبقة لهذا الطلب');
    }
    
    try {
        $db->beginTransaction();
        
        // حساب العمولة والسعر الإجمالي
        $commission = ($originalPrice * $commissionPercentage) / 100;
        $totalPrice = $originalPrice + $commission;
        
        // إنشاء الفاتورة
        $invoiceId = $db->insert('invoices', [
            'order_id' => $orderId,
            'offer_id' => $offerId,
            'original_price' => $originalPrice,
            'commission' => $commission,
            'commission_percentage' => $commissionPercentage,
            'total_price' => $totalPrice,
            'bank_details' => $bankDetails,
            'status' => 'pending'
        ]);
        
        // تحديث حالة الطلب
        $db->update('orders', ['status' => 'invoiced'], 'id = ?', [$orderId]);
        
        // تحديث العرض المختار
        $db->update('offers', ['is_selected' => 1], 'id = ?', [$offerId]);
        
        $db->commit();
        
        // جلب الفاتورة الجديدة
        $invoice = $db->fetch("
            SELECT 
                i.*,
                o.title as order_title,
                u.name as customer_name,
                s.name as shop_name
            FROM invoices i
            LEFT JOIN orders o ON i.order_id = o.id
            LEFT JOIN users u ON o.customer_id = u.id
            LEFT JOIN offers of ON i.offer_id = of.id
            LEFT JOIN users s ON of.shop_id = s.id
            WHERE i.id = ?
        ", [$invoiceId]);
        
        successResponse($invoice, 'تم إنشاء الفاتورة بنجاح');
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

function handleUpdateInvoice($db, $request) {
    $invoiceId = $request['invoice_id'] ?? '';
    $status = sanitize($request['status'] ?? '');
    $paymentReceiptImage = sanitize($request['payment_receipt_image'] ?? '');
    
    if (empty($invoiceId)) {
        errorResponse('معرف الفاتورة مطلوب');
    }
    
    $updateData = [];
    
    if (!empty($status)) {
        $validStatuses = ['pending', 'paid', 'confirmed'];
        if (!in_array($status, $validStatuses)) {
            errorResponse('حالة غير صحيحة');
        }
        $updateData['status'] = $status;
    }
    
    if (!empty($paymentReceiptImage)) {
        $updateData['payment_receipt_image'] = $paymentReceiptImage;
        if (empty($status)) {
            $updateData['status'] = 'paid';
        }
    }
    
    if (empty($updateData)) {
        errorResponse('لا توجد بيانات للتحديث');
    }
    
    $db->update('invoices', $updateData, 'id = ?', [$invoiceId]);
    
    // إذا تم تأكيد الدفع، تحديث حالة الطلب
    if (isset($updateData['status']) && $updateData['status'] === 'confirmed') {
        $invoice = $db->fetch("SELECT order_id FROM invoices WHERE id = ?", [$invoiceId]);
        if ($invoice) {
            $db->update('orders', ['status' => 'completed'], 'id = ?', [$invoice['order_id']]);
        }
    }
    
    successResponse([], 'تم تحديث الفاتورة بنجاح');
}
?>
