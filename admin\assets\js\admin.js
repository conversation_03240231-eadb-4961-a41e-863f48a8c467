/**
 * ملف JavaScript للوحة تحكم قطع غيار السيارات
 */

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // تفعيل tooltips
    initializeTooltips();
    
    // تفعيل الرسوم المتحركة
    initializeAnimations();
    
    // تفعيل التحديث التلقائي
    initializeAutoRefresh();
    
    // تفعيل البحث المباشر
    initializeLiveSearch();
    
    // تفعيل التنبيهات
    initializeNotifications();
}

/**
 * تفعيل tooltips
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تفعيل الرسوم المتحركة
 */
function initializeAnimations() {
    // إضافة تأثير fade-in للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
    
    // تأثير hover للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

/**
 * تفعيل التحديث التلقائي للإحصائيات
 */
function initializeAutoRefresh() {
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(function() {
        refreshStatistics();
    }, 30000);
}

/**
 * تحديث الإحصائيات
 */
function refreshStatistics() {
    fetch('api/get_statistics.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatisticsDisplay(data.statistics);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

/**
 * تحديث عرض الإحصائيات
 */
function updateStatisticsDisplay(stats) {
    // تحديث عدد الطلبات
    const totalOrdersElement = document.querySelector('[data-stat="total-orders"]');
    if (totalOrdersElement) {
        animateNumber(totalOrdersElement, stats.total_orders);
    }
    
    // تحديث الطلبات المعلقة
    const pendingOrdersElement = document.querySelector('[data-stat="pending-orders"]');
    if (pendingOrdersElement) {
        animateNumber(pendingOrdersElement, stats.pending_orders);
    }
    
    // تحديث الأرباح
    const revenueElement = document.querySelector('[data-stat="revenue"]');
    if (revenueElement) {
        revenueElement.textContent = formatPrice(stats.revenue);
    }
}

/**
 * تحريك الأرقام
 */
function animateNumber(element, targetNumber) {
    const currentNumber = parseInt(element.textContent) || 0;
    const increment = (targetNumber - currentNumber) / 20;
    let current = currentNumber;
    
    const timer = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= targetNumber) || (increment < 0 && current <= targetNumber)) {
            current = targetNumber;
            clearInterval(timer);
        }
        element.textContent = Math.round(current);
    }, 50);
}

/**
 * تنسيق السعر
 */
function formatPrice(price) {
    return new Intl.NumberFormat('ar-OM', {
        style: 'currency',
        currency: 'OMR',
        minimumFractionDigits: 3
    }).format(price);
}

/**
 * تفعيل البحث المباشر
 */
function initializeLiveSearch() {
    const searchInputs = document.querySelectorAll('input[type="search"], input[name="search"]');
    
    searchInputs.forEach(input => {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                performLiveSearch(this.value, this.dataset.target);
            }, 500);
        });
    });
}

/**
 * تنفيذ البحث المباشر
 */
function performLiveSearch(query, target) {
    if (!target) return;
    
    fetch(`api/search.php?q=${encodeURIComponent(query)}&target=${target}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSearchResults(data.results, target);
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
        });
}

/**
 * تحديث نتائج البحث
 */
function updateSearchResults(results, target) {
    const resultsContainer = document.querySelector(`[data-search-results="${target}"]`);
    if (!resultsContainer) return;
    
    resultsContainer.innerHTML = '';
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="text-center py-4"><p class="text-muted">لا توجد نتائج</p></div>';
        return;
    }
    
    results.forEach(result => {
        const resultElement = createSearchResultElement(result, target);
        resultsContainer.appendChild(resultElement);
    });
}

/**
 * إنشاء عنصر نتيجة البحث
 */
function createSearchResultElement(result, target) {
    const div = document.createElement('div');
    div.className = 'search-result-item p-3 border-bottom';
    
    switch (target) {
        case 'orders':
            div.innerHTML = `
                <div class="d-flex justify-content-between">
                    <div>
                        <h6><a href="order_details.php?id=${result.id}">${result.title}</a></h6>
                        <small class="text-muted">${result.customer_name} - ${result.car_model}</small>
                    </div>
                    <span class="badge bg-${getStatusColor(result.status)}">${getStatusText(result.status)}</span>
                </div>
            `;
            break;
        case 'customers':
            div.innerHTML = `
                <div>
                    <h6>${result.name}</h6>
                    <small class="text-muted">${result.email} - ${result.phone}</small>
                </div>
            `;
            break;
    }
    
    return div;
}

/**
 * الحصول على لون الحالة
 */
function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'has_offers': 'info',
        'negotiating': 'primary',
        'agreed': 'success',
        'completed': 'success',
        'cancelled': 'danger'
    };
    return colors[status] || 'secondary';
}

/**
 * الحصول على نص الحالة
 */
function getStatusText(status) {
    const texts = {
        'pending': 'في الانتظار',
        'has_offers': 'لديها عروض',
        'negotiating': 'تفاوض',
        'agreed': 'متفق عليه',
        'completed': 'مكتمل',
        'cancelled': 'ملغي'
    };
    return texts[status] || status;
}

/**
 * تفعيل التنبيهات
 */
function initializeNotifications() {
    // التحقق من التنبيهات الجديدة كل دقيقة
    setInterval(checkNotifications, 60000);
    
    // التحقق عند تحميل الصفحة
    checkNotifications();
}

/**
 * التحقق من التنبيهات
 */
function checkNotifications() {
    fetch('api/get_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.notifications.length > 0) {
                showNotifications(data.notifications);
            }
        })
        .catch(error => {
            console.error('خطأ في جلب التنبيهات:', error);
        });
}

/**
 * عرض التنبيهات
 */
function showNotifications(notifications) {
    notifications.forEach(notification => {
        showToast(notification.message, notification.type);
    });
}

/**
 * عرض رسالة toast
 */
function showToast(message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // إزالة التوست بعد إخفائه
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

/**
 * الحصول على أو إنشاء حاوية التوست
 */
function getOrCreateToastContainer() {
    let container = document.querySelector('.toast-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
    }
    return container;
}

/**
 * تأكيد الحذف
 */
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

/**
 * تحميل المحتوى بـ AJAX
 */
function loadContent(url, targetSelector) {
    const target = document.querySelector(targetSelector);
    if (!target) return;
    
    // إظهار مؤشر التحميل
    target.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div></div>';
    
    fetch(url)
        .then(response => response.text())
        .then(html => {
            target.innerHTML = html;
            // إعادة تهيئة المكونات الجديدة
            initializeTooltips();
        })
        .catch(error => {
            target.innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل المحتوى</div>';
            console.error('خطأ في تحميل المحتوى:', error);
        });
}

/**
 * تصدير البيانات
 */
function exportData(type, format = 'excel') {
    const url = `api/export.php?type=${type}&format=${format}`;
    window.open(url, '_blank');
}

/**
 * طباعة الصفحة
 */
function printPage() {
    window.print();
}

/**
 * نسخ النص إلى الحافظة
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('تم نسخ النص إلى الحافظة', 'success');
    }).catch(err => {
        console.error('خطأ في النسخ:', err);
        showToast('فشل في نسخ النص', 'danger');
    });
}

/**
 * تحديث الوقت المحلي
 */
function updateLocalTime() {
    const timeElements = document.querySelectorAll('[data-time]');
    timeElements.forEach(element => {
        const timestamp = element.dataset.time;
        const date = new Date(timestamp);
        element.textContent = date.toLocaleString('ar-OM');
    });
}

// تحديث الوقت كل ثانية
setInterval(updateLocalTime, 1000);

/**
 * تفعيل الوضع المظلم
 */
function toggleDarkMode() {
    document.body.classList.toggle('dark-mode');
    const isDark = document.body.classList.contains('dark-mode');
    localStorage.setItem('darkMode', isDark);
}

// تحميل إعدادات الوضع المظلم
if (localStorage.getItem('darkMode') === 'true') {
    document.body.classList.add('dark-mode');
}
