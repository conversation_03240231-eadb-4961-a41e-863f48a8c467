class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  final UserType userType;
  final String? profileImage;
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.userType,
    this.profileImage,
    required this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      userType: _parseUserType(json['user_type']),
      profileImage: json['profile_image'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  static UserType _parseUserType(String? type) {
    switch (type) {
      case 'customer':
        return UserType.customer;
      case 'shop':
        return UserType.shop;
      case 'mediator':
        return UserType.mediator;
      default:
        return UserType.customer;
    }
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      userType: _parseUserType(json['user_type']),
      profileImage: json['profile_image'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'user_type': userType.toString().split('.').last,
      'profile_image': profileImage,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

enum UserType {
  customer,  // عميل
  shop,      // محل
  mediator   // وسيط
}

extension UserTypeExtension on UserType {
  String get displayName {
    switch (this) {
      case UserType.customer:
        return 'عميل';
      case UserType.shop:
        return 'محل';
      case UserType.mediator:
        return 'وسيط';
    }
  }
}
