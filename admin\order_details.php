<?php
session_start();
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'mediator') {
    header('Location: login.php');
    exit;
}

$order_id = $_GET['id'] ?? 0;
if (!$order_id) {
    header('Location: orders.php');
    exit;
}

$db = getDB();

// الحصول على تفاصيل الطلب
$order = $db->fetch("
    SELECT o.*, u.name as customer_name, u.email as customer_email, u.phone as customer_phone
    FROM orders o 
    LEFT JOIN users u ON o.customer_id = u.id 
    WHERE o.id = ?
", [$order_id]);

if (!$order) {
    header('Location: orders.php');
    exit;
}

// الحصول على العروض
$offers = $db->fetchAll("
    SELECT of.*, u.name as shop_name, u.email as shop_email, u.phone as shop_phone
    FROM offers of
    LEFT JOIN users u ON of.shop_id = u.id
    WHERE of.order_id = ?
    ORDER BY of.price ASC
", [$order_id]);

// الحصول على الرسائل
$messages = $db->fetchAll("
    SELECT m.*, u.name as sender_name, u.user_type as sender_type
    FROM messages m
    LEFT JOIN users u ON m.sender_id = u.id
    WHERE m.order_id = ?
    ORDER BY m.created_at ASC
", [$order_id]);

// معالجة إرسال رسالة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $content = sanitize($_POST['content'] ?? '');
    
    if (!empty($content)) {
        $db->insert('messages', [
            'order_id' => $order_id,
            'sender_id' => $_SESSION['user_id'],
            'content' => $content
        ]);
        
        // تحديث حالة الطلب إلى "تفاوض" إذا لم تكن كذلك
        if ($order['status'] === 'has_offers') {
            $db->update('orders', ['status' => 'negotiating'], 'id = ?', [$order_id]);
        }
        
        header('Location: order_details.php?id=' . $order_id);
        exit;
    }
}

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $new_status = $_POST['status'] ?? '';
    $valid_statuses = ['pending', 'has_offers', 'negotiating', 'agreed', 'invoiced', 'paid', 'completed', 'cancelled'];
    
    if (in_array($new_status, $valid_statuses)) {
        $db->update('orders', ['status' => $new_status], 'id = ?', [$order_id]);
        header('Location: order_details.php?id=' . $order_id);
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطلب #<?php echo $order['id']; ?> - لوحة تحكم الوسيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-car"></i> قطع غيار السيارات
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo $_SESSION['user_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="orders.php">
                                <i class="fas fa-shopping-cart"></i> الطلبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="offers.php">
                                <i class="fas fa-tags"></i> العروض
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="invoices.php">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تفاصيل الطلب #<?php echo $order['id']; ?></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="orders.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للطلبات
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- Order Details -->
                    <div class="col-lg-8">
                        <!-- Order Info -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">معلومات الطلب</h5>
                                <div>
                                    <?php
                                    $statusClass = [
                                        'pending' => 'warning',
                                        'has_offers' => 'info',
                                        'negotiating' => 'primary',
                                        'agreed' => 'success',
                                        'invoiced' => 'dark',
                                        'paid' => 'success',
                                        'completed' => 'success',
                                        'cancelled' => 'danger'
                                    ];
                                    $statusText = [
                                        'pending' => 'في الانتظار',
                                        'has_offers' => 'لديها عروض',
                                        'negotiating' => 'تفاوض',
                                        'agreed' => 'متفق عليه',
                                        'invoiced' => 'تم إنشاء فاتورة',
                                        'paid' => 'تم الدفع',
                                        'completed' => 'مكتمل',
                                        'cancelled' => 'ملغي'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $statusClass[$order['status']] ?? 'secondary'; ?> fs-6">
                                        <?php echo $statusText[$order['status']] ?? $order['status']; ?>
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-tag"></i> العنوان:</h6>
                                        <p><?php echo htmlspecialchars($order['title']); ?></p>
                                        
                                        <h6><i class="fas fa-car"></i> السيارة:</h6>
                                        <p><?php echo htmlspecialchars($order['car_model'] . ' - ' . $order['car_year']); ?></p>
                                        
                                        <h6><i class="fas fa-calendar"></i> تاريخ الطلب:</h6>
                                        <p><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-user"></i> العميل:</h6>
                                        <p>
                                            <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong><br>
                                            <i class="fas fa-envelope"></i> <?php echo htmlspecialchars($order['customer_email']); ?><br>
                                            <i class="fas fa-phone"></i> <?php echo htmlspecialchars($order['customer_phone']); ?>
                                        </p>
                                    </div>
                                </div>
                                
                                <h6><i class="fas fa-align-left"></i> الوصف:</h6>
                                <p><?php echo nl2br(htmlspecialchars($order['description'])); ?></p>
                                
                                <?php if (!empty($order['images'])): ?>
                                    <?php $images = json_decode($order['images'], true); ?>
                                    <?php if (is_array($images) && !empty($images)): ?>
                                        <h6><i class="fas fa-images"></i> الصور:</h6>
                                        <div class="row">
                                            <?php foreach ($images as $image): ?>
                                                <div class="col-md-3 mb-2">
                                                    <img src="<?php echo htmlspecialchars($image); ?>" 
                                                         class="img-fluid rounded" alt="صورة الطلب">
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Offers -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">العروض المقدمة (<?php echo count($offers); ?>)</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($offers)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">لا توجد عروض حتى الآن</h6>
                                        <p class="text-muted">ستظهر هنا العروض المقدمة من المحلات</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($offers as $index => $offer): ?>
                                        <div class="card mb-3 <?php echo $index === 0 ? 'border-success' : ''; ?>">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <h6 class="card-title">
                                                                <i class="fas fa-store"></i> <?php echo htmlspecialchars($offer['shop_name']); ?>
                                                                <?php if ($index === 0): ?>
                                                                    <span class="badge bg-success ms-2">أفضل سعر</span>
                                                                <?php endif; ?>
                                                            </h6>
                                                        </div>
                                                        <p class="card-text"><?php echo nl2br(htmlspecialchars($offer['description'])); ?></p>
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock"></i> مدة التوصيل: <?php echo $offer['delivery_days']; ?> أيام
                                                            | <i class="fas fa-calendar"></i> <?php echo date('Y-m-d H:i', strtotime($offer['created_at'])); ?>
                                                        </small>
                                                    </div>
                                                    <div class="col-md-4 text-end">
                                                        <h4 class="text-success">
                                                            <?php echo formatPrice($offer['price']); ?>
                                                        </h4>
                                                        <div class="btn-group-vertical w-100">
                                                            <a href="create_invoice.php?order_id=<?php echo $order_id; ?>&offer_id=<?php echo $offer['id']; ?>" 
                                                               class="btn btn-success btn-sm">
                                                                <i class="fas fa-file-invoice"></i> إنشاء فاتورة
                                                            </a>
                                                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                                                    data-bs-toggle="modal" data-bs-target="#contactShopModal<?php echo $offer['id']; ?>">
                                                                <i class="fas fa-phone"></i> تواصل مع المحل
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <?php if (!empty($offer['images'])): ?>
                                                    <?php $offerImages = json_decode($offer['images'], true); ?>
                                                    <?php if (is_array($offerImages) && !empty($offerImages)): ?>
                                                        <div class="mt-3">
                                                            <h6>صور العرض:</h6>
                                                            <div class="row">
                                                                <?php foreach ($offerImages as $image): ?>
                                                                    <div class="col-md-2 mb-2">
                                                                        <img src="<?php echo htmlspecialchars($image); ?>" 
                                                                             class="img-fluid rounded" alt="صورة العرض">
                                                                    </div>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Contact Shop Modal -->
                                        <div class="modal fade" id="contactShopModal<?php echo $offer['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">تواصل مع <?php echo htmlspecialchars($offer['shop_name']); ?></h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p><strong>اسم المحل:</strong> <?php echo htmlspecialchars($offer['shop_name']); ?></p>
                                                        <p><strong>البريد الإلكتروني:</strong> 
                                                            <a href="mailto:<?php echo $offer['shop_email']; ?>"><?php echo htmlspecialchars($offer['shop_email']); ?></a>
                                                        </p>
                                                        <p><strong>رقم الهاتف:</strong> 
                                                            <a href="tel:<?php echo $offer['shop_phone']; ?>"><?php echo htmlspecialchars($offer['shop_phone']); ?></a>
                                                        </p>
                                                        <p><strong>السعر المقدم:</strong> <?php echo formatPrice($offer['price']); ?></p>
                                                        <p><strong>مدة التوصيل:</strong> <?php echo $offer['delivery_days']; ?> أيام</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                        <a href="tel:<?php echo $offer['shop_phone']; ?>" class="btn btn-primary">
                                                            <i class="fas fa-phone"></i> اتصال
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Status Update -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">تحديث حالة الطلب</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <select name="status" class="form-select">
                                            <option value="pending" <?php echo $order['status'] === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                            <option value="has_offers" <?php echo $order['status'] === 'has_offers' ? 'selected' : ''; ?>>لديها عروض</option>
                                            <option value="negotiating" <?php echo $order['status'] === 'negotiating' ? 'selected' : ''; ?>>تفاوض</option>
                                            <option value="agreed" <?php echo $order['status'] === 'agreed' ? 'selected' : ''; ?>>متفق عليه</option>
                                            <option value="invoiced" <?php echo $order['status'] === 'invoiced' ? 'selected' : ''; ?>>تم إنشاء فاتورة</option>
                                            <option value="paid" <?php echo $order['status'] === 'paid' ? 'selected' : ''; ?>>تم الدفع</option>
                                            <option value="completed" <?php echo $order['status'] === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                            <option value="cancelled" <?php echo $order['status'] === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                                        </select>
                                    </div>
                                    <button type="submit" name="update_status" class="btn btn-primary w-100">
                                        <i class="fas fa-save"></i> تحديث الحالة
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Messages -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">المحادثة مع العميل</h6>
                            </div>
                            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                <?php if (empty($messages)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-comments fa-2x text-muted mb-2"></i>
                                        <p class="text-muted">لا توجد رسائل حتى الآن</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($messages as $message): ?>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <strong class="<?php echo $message['sender_type'] === 'mediator' ? 'text-primary' : 'text-success'; ?>">
                                                    <?php echo htmlspecialchars($message['sender_name']); ?>
                                                    <?php if ($message['sender_type'] === 'mediator'): ?>
                                                        <span class="badge bg-primary">وسيط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">عميل</span>
                                                    <?php endif; ?>
                                                </strong>
                                                <small class="text-muted"><?php echo date('H:i', strtotime($message['created_at'])); ?></small>
                                            </div>
                                            <p class="mb-1"><?php echo nl2br(htmlspecialchars($message['content'])); ?></p>
                                        </div>
                                        <hr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer">
                                <form method="POST">
                                    <div class="mb-2">
                                        <textarea name="content" class="form-control" rows="3" 
                                                  placeholder="اكتب رسالة للعميل..." required></textarea>
                                    </div>
                                    <button type="submit" name="send_message" class="btn btn-primary w-100">
                                        <i class="fas fa-paper-plane"></i> إرسال الرسالة
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
