<?php
session_start();
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'mediator') {
    header('Location: login.php');
    exit;
}

$db = getDB();

// فلترة الطلبات
$status_filter = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';

// بناء الاستعلام
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = 'o.status = ?';
    $params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = '(o.title LIKE ? OR o.description LIKE ? OR u.name LIKE ?)';
    $search_term = '%' . $search . '%';
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على الطلبات
$orders = $db->fetchAll("
    SELECT o.*, u.name as customer_name, u.email as customer_email, u.phone as customer_phone,
           COUNT(of.id) as offers_count,
           MIN(of.price) as min_price,
           MAX(of.price) as max_price,
           COUNT(m.id) as messages_count
    FROM orders o 
    LEFT JOIN users u ON o.customer_id = u.id 
    LEFT JOIN offers of ON o.id = of.order_id
    LEFT JOIN messages m ON o.id = m.order_id
    {$where_clause}
    GROUP BY o.id
    ORDER BY o.created_at DESC
", $params);

// إحصائيات سريعة
$stats = [
    'all' => $db->count('orders'),
    'pending' => $db->count('orders', 'status = ?', ['pending']),
    'has_offers' => $db->count('orders', 'status = ?', ['has_offers']),
    'negotiating' => $db->count('orders', 'status = ?', ['negotiating']),
    'agreed' => $db->count('orders', 'status = ?', ['agreed']),
    'completed' => $db->count('orders', 'status = ?', ['completed'])
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات - لوحة تحكم الوسيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-car"></i> قطع غيار السيارات
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo $_SESSION['user_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="orders.php">
                                <i class="fas fa-shopping-cart"></i> الطلبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="offers.php">
                                <i class="fas fa-tags"></i> العروض
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="invoices.php">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users"></i> العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="shops.php">
                                <i class="fas fa-store"></i> المحلات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة الطلبات</h1>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card text-center <?php echo $status_filter === 'all' ? 'border-primary' : ''; ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $stats['all']; ?></h5>
                                <p class="card-text">جميع الطلبات</p>
                                <a href="?status=all&search=<?php echo urlencode($search); ?>" 
                                   class="btn btn-sm <?php echo $status_filter === 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    عرض
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center <?php echo $status_filter === 'pending' ? 'border-warning' : ''; ?>">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?php echo $stats['pending']; ?></h5>
                                <p class="card-text">في الانتظار</p>
                                <a href="?status=pending&search=<?php echo urlencode($search); ?>" 
                                   class="btn btn-sm <?php echo $status_filter === 'pending' ? 'btn-warning' : 'btn-outline-warning'; ?>">
                                    عرض
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center <?php echo $status_filter === 'has_offers' ? 'border-info' : ''; ?>">
                            <div class="card-body">
                                <h5 class="card-title text-info"><?php echo $stats['has_offers']; ?></h5>
                                <p class="card-text">لديها عروض</p>
                                <a href="?status=has_offers&search=<?php echo urlencode($search); ?>" 
                                   class="btn btn-sm <?php echo $status_filter === 'has_offers' ? 'btn-info' : 'btn-outline-info'; ?>">
                                    عرض
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center <?php echo $status_filter === 'negotiating' ? 'border-primary' : ''; ?>">
                            <div class="card-body">
                                <h5 class="card-title text-primary"><?php echo $stats['negotiating']; ?></h5>
                                <p class="card-text">تفاوض</p>
                                <a href="?status=negotiating&search=<?php echo urlencode($search); ?>" 
                                   class="btn btn-sm <?php echo $status_filter === 'negotiating' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    عرض
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center <?php echo $status_filter === 'agreed' ? 'border-success' : ''; ?>">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?php echo $stats['agreed']; ?></h5>
                                <p class="card-text">متفق عليه</p>
                                <a href="?status=agreed&search=<?php echo urlencode($search); ?>" 
                                   class="btn btn-sm <?php echo $status_filter === 'agreed' ? 'btn-success' : 'btn-outline-success'; ?>">
                                    عرض
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center <?php echo $status_filter === 'completed' ? 'border-success' : ''; ?>">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?php echo $stats['completed']; ?></h5>
                                <p class="card-text">مكتمل</p>
                                <a href="?status=completed&search=<?php echo urlencode($search); ?>" 
                                   class="btn btn-sm <?php echo $status_filter === 'completed' ? 'btn-success' : 'btn-outline-success'; ?>">
                                    عرض
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="البحث في العنوان، الوصف، أو اسم العميل...">
                            </div>
                            <div class="col-md-4">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الحالات</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                    <option value="has_offers" <?php echo $status_filter === 'has_offers' ? 'selected' : ''; ?>>لديها عروض</option>
                                    <option value="negotiating" <?php echo $status_filter === 'negotiating' ? 'selected' : ''; ?>>تفاوض</option>
                                    <option value="agreed" <?php echo $status_filter === 'agreed' ? 'selected' : ''; ?>>متفق عليه</option>
                                    <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">قائمة الطلبات (<?php echo count($orders); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العنوان</th>
                                        <th>العميل</th>
                                        <th>السيارة</th>
                                        <th>العروض</th>
                                        <th>الرسائل</th>
                                        <th>نطاق الأسعار</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orders as $order): ?>
                                    <tr>
                                        <td><strong>#<?php echo $order['id']; ?></strong></td>
                                        <td>
                                            <a href="order_details.php?id=<?php echo $order['id']; ?>" 
                                               class="text-decoration-none">
                                                <?php echo htmlspecialchars($order['title']); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($order['customer_phone']); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($order['car_model']); ?><br>
                                            <small class="text-muted"><?php echo $order['car_year']; ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $order['offers_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $order['messages_count']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($order['offers_count'] > 0): ?>
                                                <small>
                                                    <?php echo formatPrice($order['min_price']); ?><br>
                                                    إلى <?php echo formatPrice($order['max_price']); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">لا توجد عروض</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'pending' => 'warning',
                                                'has_offers' => 'info',
                                                'negotiating' => 'primary',
                                                'agreed' => 'success',
                                                'invoiced' => 'dark',
                                                'paid' => 'success',
                                                'completed' => 'success',
                                                'cancelled' => 'danger'
                                            ];
                                            $statusText = [
                                                'pending' => 'في الانتظار',
                                                'has_offers' => 'لديها عروض',
                                                'negotiating' => 'تفاوض',
                                                'agreed' => 'متفق عليه',
                                                'invoiced' => 'تم إنشاء فاتورة',
                                                'paid' => 'تم الدفع',
                                                'completed' => 'مكتمل',
                                                'cancelled' => 'ملغي'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $statusClass[$order['status']] ?? 'secondary'; ?>">
                                                <?php echo $statusText[$order['status']] ?? $order['status']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="order_details.php?id=<?php echo $order['id']; ?>" 
                                                   class="btn btn-sm btn-primary" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if ($order['offers_count'] > 0 && $order['status'] === 'has_offers'): ?>
                                                <a href="create_invoice.php?order_id=<?php echo $order['id']; ?>" 
                                                   class="btn btn-sm btn-success" title="إنشاء فاتورة">
                                                    <i class="fas fa-file-invoice"></i>
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            
                            <?php if (empty($orders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد طلبات</h5>
                                <p class="text-muted">لم يتم العثور على طلبات تطابق معايير البحث</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
