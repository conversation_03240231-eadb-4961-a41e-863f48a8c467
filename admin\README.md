# لوحة تحكم قطع غيار السيارات

## نظرة عامة
لوحة تحكم PHP/MySQL لإدارة تطبيق قطع غيار السيارات. تتيح للوسيط إدارة الطلبات والعروض والفواتير والتواصل مع العملاء.

## المتطلبات
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- مكتبة PDO PHP

## التثبيت

### 1. إعد<PERSON> قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE carspart_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الجداول
mysql -u root -p carspart_db < database/carspart_db.sql
```

### 2. <PERSON><PERSON>د<PERSON> الاتصال بقاعدة البيانات
قم بتعديل ملف `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'carspart_db';
private $username = 'your_username';
private $password = 'your_password';
```

### 3. إعداد الخادم
- انسخ ملفات المشروع إلى مجلد الخادم
- تأكد من أن PHP يدعم PDO و MySQL
- قم بتعيين صلاحيات الكتابة لمجلد `uploads/`

### 4. إنشاء مجلدات الرفع
```bash
mkdir uploads
mkdir uploads/orders
mkdir uploads/offers
mkdir uploads/messages
mkdir uploads/invoices
chmod 755 uploads -R
```

## بيانات تسجيل الدخول الافتراضية

### الوسيط (المدير)
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

### عملاء تجريبيون
- **العميل 1:** <EMAIL> / password
- **العميل 2:** <EMAIL> / password

### محلات تجريبية
- **محل الشارقة:** <EMAIL> / password
- **محل دبي:** <EMAIL> / password

## الميزات الرئيسية

### 1. لوحة التحكم الرئيسية
- إحصائيات شاملة للطلبات والعروض
- الطلبات الحديثة والعروض الجديدة
- مؤشرات الأداء والأرباح

### 2. إدارة الطلبات
- عرض جميع الطلبات مع فلترة متقدمة
- تفاصيل كاملة لكل طلب
- إدارة حالات الطلبات
- نظام المحادثة مع العملاء

### 3. إدارة العروض
- عرض جميع العروض المقدمة
- مقارنة الأسعار والمواصفات
- اختيار أفضل العروض
- التواصل مع المحلات

### 4. نظام الفواتير
- إنشاء فواتير تلقائية
- حساب العمولات
- تتبع المدفوعات
- إدارة إيصالات التحويل

### 5. إدارة العملاء والمحلات
- قوائم شاملة للمستخدمين
- إحصائيات الأداء
- إدارة الحسابات

## هيكل الملفات

```
admin/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── database/
│   └── carspart_db.sql       # ملف قاعدة البيانات
├── assets/
│   ├── css/
│   │   └── admin.css         # ملفات التصميم
│   └── js/
│       └── admin.js          # ملفات JavaScript
├── uploads/                  # مجلد الملفات المرفوعة
├── index.php                 # الصفحة الرئيسية
├── login.php                 # صفحة تسجيل الدخول
├── logout.php                # تسجيل الخروج
├── orders.php                # إدارة الطلبات
├── order_details.php         # تفاصيل الطلب
├── create_invoice.php        # إنشاء فاتورة
└── README.md                 # هذا الملف
```

## API للتطبيق المحمول

### نقاط النهاية الأساسية

#### المصادقة
```
POST /api/auth/login
POST /api/auth/register
POST /api/auth/logout
```

#### الطلبات
```
GET /api/orders                # جلب الطلبات
POST /api/orders               # إنشاء طلب جديد
GET /api/orders/{id}           # تفاصيل طلب
PUT /api/orders/{id}           # تحديث طلب
```

#### العروض
```
GET /api/offers                # جلب العروض
POST /api/offers               # إنشاء عرض جديد
GET /api/orders/{id}/offers    # عروض طلب معين
```

#### الرسائل
```
GET /api/orders/{id}/messages  # رسائل طلب
POST /api/orders/{id}/messages # إرسال رسالة
```

#### الفواتير
```
GET /api/invoices              # جلب الفواتير
POST /api/invoices             # إنشاء فاتورة
PUT /api/invoices/{id}         # تحديث فاتورة
```

## الأمان

### 1. حماية قاعدة البيانات
- استخدام Prepared Statements
- تشفير كلمات المرور
- التحقق من صحة البيانات

### 2. حماية الجلسات
- إدارة آمنة للجلسات
- انتهاء صلاحية تلقائي
- حماية من CSRF

### 3. رفع الملفات
- فلترة أنواع الملفات
- فحص أحجام الملفات
- تسمية آمنة للملفات

## التخصيص

### 1. الإعدادات العامة
قم بتعديل جدول `settings` في قاعدة البيانات:
```sql
UPDATE settings SET setting_value = '15' WHERE setting_key = 'commission_percentage';
UPDATE settings SET setting_value = 'بيانات البنك الجديدة' WHERE setting_key = 'bank_details';
```

### 2. تخصيص التصميم
- عدل ملف `assets/css/admin.css` للتصميم
- أضف شعارك في `assets/images/`
- غير الألوان في متغيرات CSS

### 3. إضافة ميزات جديدة
- أنشئ ملفات PHP جديدة
- أضف روابط في القائمة الجانبية
- حدث قاعدة البيانات حسب الحاجة

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```
تحقق من:
- بيانات الاتصال في config/database.php
- تشغيل خدمة MySQL
- صلاحيات المستخدم
```

#### مشاكل رفع الملفات
```
تحقق من:
- صلاحيات مجلد uploads/
- حجم الملف المسموح في PHP
- نوع الملف المرفوع
```

#### مشاكل الجلسات
```
تحقق من:
- إعدادات session في PHP
- صلاحيات مجلد tmp/
- إعدادات الكوكيز
```

## الدعم والتطوير

### متطلبات التطوير
- معرفة بـ PHP و MySQL
- فهم Bootstrap و JavaScript
- خبرة في تطوير APIs

### المساهمة
1. Fork المشروع
2. أنشئ branch جديد
3. اعمل التغييرات المطلوبة
4. اختبر التغييرات
5. أرسل Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## جهات الاتصال
- المطور: فريق التطوير
- البريد الإلكتروني: <EMAIL>
- الموقع: www.carspart.com

---

**ملاحظة:** هذا النظام مصمم للاستخدام التجاري. تأكد من اتباع أفضل ممارسات الأمان قبل النشر في بيئة الإنتاج.
