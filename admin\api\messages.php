<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];
$request = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            handleGetMessages($db);
            break;
            
        case 'POST':
            handleSendMessage($db, $request);
            break;
            
        default:
            errorResponse('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    errorResponse('Server error: ' . $e->getMessage(), 500);
}

function handleGetMessages($db) {
    $orderId = $_GET['order_id'] ?? '';
    
    if (empty($orderId)) {
        errorResponse('معرف الطلب مطلوب');
    }
    
    $messages = $db->fetchAll("
        SELECT m.*, u.name as sender_name, u.user_type as sender_type
        FROM messages m
        LEFT JOIN users u ON m.sender_id = u.id
        WHERE m.order_id = ?
        ORDER BY m.created_at ASC
    ", [$orderId]);
    
    // تحويل JSON إلى array
    foreach ($messages as &$message) {
        if ($message['images']) {
            $message['images'] = json_decode($message['images'], true) ?: [];
        } else {
            $message['images'] = [];
        }
    }
    
    successResponse($messages);
}

function handleSendMessage($db, $request) {
    $orderId = $request['order_id'] ?? '';
    $senderId = $request['sender_id'] ?? '';
    $senderName = sanitize($request['sender_name'] ?? '');
    $senderType = sanitize($request['sender_type'] ?? '');
    $content = sanitize($request['content'] ?? '');
    $images = $request['images'] ?? [];
    
    if (empty($orderId) || empty($senderId) || empty($content)) {
        errorResponse('معرف الطلب ومعرف المرسل والمحتوى مطلوبة');
    }
    
    // التحقق من وجود الطلب
    $order = $db->fetch("SELECT id FROM orders WHERE id = ?", [$orderId]);
    if (!$order) {
        errorResponse('الطلب غير موجود');
    }
    
    // التحقق من وجود المرسل
    $sender = $db->fetch("SELECT id, name, user_type FROM users WHERE id = ?", [$senderId]);
    if (!$sender) {
        errorResponse('المرسل غير موجود');
    }
    
    $messageId = $db->insert('messages', [
        'order_id' => $orderId,
        'sender_id' => $senderId,
        'content' => $content,
        'images' => json_encode($images)
    ]);
    
    // جلب الرسالة الجديدة
    $message = $db->fetch("
        SELECT m.*, u.name as sender_name, u.user_type as sender_type
        FROM messages m
        LEFT JOIN users u ON m.sender_id = u.id
        WHERE m.id = ?
    ", [$messageId]);
    
    $message['images'] = json_decode($message['images'], true) ?: [];
    
    successResponse($message, 'تم إرسال الرسالة بنجاح');
}
?>
