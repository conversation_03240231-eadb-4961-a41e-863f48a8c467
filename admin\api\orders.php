<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];
$request = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            handleGetOrders($db);
            break;
            
        case 'POST':
            handleCreateOrder($db, $request);
            break;
            
        case 'PUT':
            handleUpdateOrder($db, $request);
            break;
            
        default:
            errorResponse('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    errorResponse('Server error: ' . $e->getMessage(), 500);
}

function handleGetOrders($db) {
    $userType = $_GET['user_type'] ?? '';
    $userId = $_GET['user_id'] ?? '';
    
    if (empty($userType) || empty($userId)) {
        errorResponse('نوع المستخدم ومعرف المستخدم مطلوبان');
    }
    
    $whereClause = '';
    $params = [];
    
    switch ($userType) {
        case 'customer':
            $whereClause = 'WHERE o.customer_id = ?';
            $params = [$userId];
            break;
            
        case 'shop':
            // المحلات ترى الطلبات المتاحة للعروض
            $whereClause = 'WHERE o.status IN (?, ?)';
            $params = ['pending', 'has_offers'];
            break;
            
        case 'mediator':
            // الوسيط يرى جميع الطلبات
            $whereClause = '';
            $params = [];
            break;
            
        default:
            errorResponse('نوع مستخدم غير صحيح');
    }
    
    $sql = "
        SELECT 
            o.*,
            u.name as customer_name,
            u.email as customer_email,
            u.phone as customer_phone,
            COUNT(of.id) as offers_count,
            MIN(of.price) as min_price,
            MAX(of.price) as max_price
        FROM orders o 
        LEFT JOIN users u ON o.customer_id = u.id 
        LEFT JOIN offers of ON o.id = of.order_id
        {$whereClause}
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ";
    
    $orders = $db->fetchAll($sql, $params);
    
    // جلب العروض والرسائل لكل طلب
    foreach ($orders as &$order) {
        // جلب العروض
        $offers = $db->fetchAll("
            SELECT of.*, u.name as shop_name, u.email as shop_email, u.phone as shop_phone
            FROM offers of
            LEFT JOIN users u ON of.shop_id = u.id
            WHERE of.order_id = ?
            ORDER BY of.price ASC
        ", [$order['id']]);
        
        // جلب الرسائل
        $messages = $db->fetchAll("
            SELECT m.*, u.name as sender_name, u.user_type as sender_type
            FROM messages m
            LEFT JOIN users u ON m.sender_id = u.id
            WHERE m.order_id = ?
            ORDER BY m.created_at ASC
        ", [$order['id']]);
        
        // جلب الفاتورة إن وجدت
        $invoice = $db->fetch("
            SELECT * FROM invoices WHERE order_id = ?
        ", [$order['id']]);
        
        $order['offers'] = $offers ?: [];
        $order['messages'] = $messages ?: [];
        $order['invoice'] = $invoice ?: null;
        
        // تحويل JSON إلى array
        if ($order['images']) {
            $order['images'] = json_decode($order['images'], true) ?: [];
        } else {
            $order['images'] = [];
        }
    }
    
    successResponse($orders);
}

function handleCreateOrder($db, $request) {
    $customerId = $request['customer_id'] ?? '';
    $customerName = sanitize($request['customer_name'] ?? '');
    $title = sanitize($request['title'] ?? '');
    $description = sanitize($request['description'] ?? '');
    $carModel = sanitize($request['car_model'] ?? '');
    $carYear = sanitize($request['car_year'] ?? '');
    $images = $request['images'] ?? [];
    
    if (empty($customerId) || empty($title) || empty($description) || empty($carModel) || empty($carYear)) {
        errorResponse('جميع الحقول مطلوبة');
    }
    
    // التحقق من وجود العميل
    $customer = $db->fetch("SELECT id, name FROM users WHERE id = ? AND user_type = 'customer'", [$customerId]);
    if (!$customer) {
        errorResponse('العميل غير موجود');
    }
    
    $orderId = $db->insert('orders', [
        'customer_id' => $customerId,
        'title' => $title,
        'description' => $description,
        'car_model' => $carModel,
        'car_year' => $carYear,
        'images' => json_encode($images),
        'status' => 'pending'
    ]);
    
    // جلب الطلب الجديد
    $order = $db->fetch("
        SELECT o.*, u.name as customer_name
        FROM orders o 
        LEFT JOIN users u ON o.customer_id = u.id 
        WHERE o.id = ?
    ", [$orderId]);
    
    $order['offers'] = [];
    $order['messages'] = [];
    $order['invoice'] = null;
    $order['images'] = json_decode($order['images'], true) ?: [];
    
    successResponse($order, 'تم إنشاء الطلب بنجاح');
}

function handleUpdateOrder($db, $request) {
    $orderId = $request['order_id'] ?? '';
    $status = sanitize($request['status'] ?? '');
    
    if (empty($orderId) || empty($status)) {
        errorResponse('معرف الطلب والحالة مطلوبان');
    }
    
    $validStatuses = ['pending', 'has_offers', 'negotiating', 'agreed', 'invoiced', 'paid', 'completed', 'cancelled'];
    if (!in_array($status, $validStatuses)) {
        errorResponse('حالة غير صحيحة');
    }
    
    $db->update('orders', ['status' => $status], 'id = ?', [$orderId]);
    
    successResponse([], 'تم تحديث حالة الطلب بنجاح');
}
?>
