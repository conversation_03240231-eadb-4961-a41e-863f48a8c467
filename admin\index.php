<?php
session_start();
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'mediator') {
    header('Location: login.php');
    exit;
}

$db = getDB();

// إحصائيات عامة
$totalOrders = $db->count('orders');
$pendingOrders = $db->count('orders', 'status = ?', ['pending']);
$ordersWithOffers = $db->count('orders', 'status = ?', ['has_offers']);
$completedOrders = $db->count('orders', 'status = ?', ['completed']);

$totalCustomers = $db->count('users', 'user_type = ?', ['customer']);
$totalShops = $db->count('users', 'user_type = ?', ['shop']);
$totalOffers = $db->count('offers');

// الطلبات الحديثة
$recentOrders = $db->fetchAll("
    SELECT o.*, u.name as customer_name, 
           COUNT(of.id) as offers_count
    FROM orders o 
    LEFT JOIN users u ON o.customer_id = u.id 
    LEFT JOIN offers of ON o.id = of.order_id
    GROUP BY o.id
    ORDER BY o.created_at DESC 
    LIMIT 5
");

// العروض الحديثة
$recentOffers = $db->fetchAll("
    SELECT of.*, o.title as order_title, u.name as shop_name
    FROM offers of
    LEFT JOIN orders o ON of.order_id = o.id
    LEFT JOIN users u ON of.shop_id = u.id
    ORDER BY of.created_at DESC
    LIMIT 5
");

// إحصائيات الأرباح
$totalRevenue = $db->fetch("SELECT SUM(commission) as total FROM invoices WHERE status = 'confirmed'");
$monthlyRevenue = $db->fetch("
    SELECT SUM(commission) as total 
    FROM invoices 
    WHERE status = 'confirmed' 
    AND MONTH(created_at) = MONTH(CURRENT_DATE())
    AND YEAR(created_at) = YEAR(CURRENT_DATE())
");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الوسيط - قطع غيار السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-car"></i> قطع غيار السيارات
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo $_SESSION['user_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit"></i> الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="index.php">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="orders.php">
                                <i class="fas fa-shopping-cart"></i> الطلبات
                                <?php if ($pendingOrders > 0): ?>
                                    <span class="badge bg-danger"><?php echo $pendingOrders; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="offers.php">
                                <i class="fas fa-tags"></i> العروض
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="invoices.php">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users"></i> العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="shops.php">
                                <i class="fas fa-store"></i> المحلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i> التقارير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">لوحة التحكم</h1>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي الطلبات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalOrders; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            طلبات في الانتظار
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $pendingOrders; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-hourglass-half fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            طلبات لديها عروض
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $ordersWithOffers; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-tags fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            الأرباح الشهرية
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo formatPrice($monthlyRevenue['total'] ?? 0); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders and Offers -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">الطلبات الحديثة</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>العنوان</th>
                                                <th>العميل</th>
                                                <th>العروض</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentOrders as $order): ?>
                                            <tr>
                                                <td>
                                                    <a href="order_details.php?id=<?php echo $order['id']; ?>">
                                                        <?php echo htmlspecialchars($order['title']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $order['offers_count']; ?></span>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = [
                                                        'pending' => 'warning',
                                                        'has_offers' => 'info',
                                                        'negotiating' => 'primary',
                                                        'agreed' => 'success',
                                                        'completed' => 'success'
                                                    ];
                                                    $statusText = [
                                                        'pending' => 'في الانتظار',
                                                        'has_offers' => 'لديها عروض',
                                                        'negotiating' => 'تفاوض',
                                                        'agreed' => 'متفق عليه',
                                                        'completed' => 'مكتمل'
                                                    ];
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusClass[$order['status']] ?? 'secondary'; ?>">
                                                        <?php echo $statusText[$order['status']] ?? $order['status']; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center">
                                    <a href="orders.php" class="btn btn-primary">عرض جميع الطلبات</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">العروض الحديثة</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>الطلب</th>
                                                <th>المحل</th>
                                                <th>السعر</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentOffers as $offer): ?>
                                            <tr>
                                                <td>
                                                    <a href="order_details.php?id=<?php echo $offer['order_id']; ?>">
                                                        <?php echo htmlspecialchars($offer['order_title']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo htmlspecialchars($offer['shop_name']); ?></td>
                                                <td><?php echo formatPrice($offer['price']); ?></td>
                                                <td><?php echo date('Y-m-d', strtotime($offer['created_at'])); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center">
                                    <a href="offers.php" class="btn btn-primary">عرض جميع العروض</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/admin.js"></script>
</body>
</html>
