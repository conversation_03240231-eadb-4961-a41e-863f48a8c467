^C:\USERS\<USER>\DESKTOP\CARSPART\BUILD\WINDOWS\X64\CMAKEFILES\A74A8B0B0B97DF705FA3AE26765A39FC\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter "PROJECT_DIR=C:\Users\<USER>\Desktop\carspart" FLUTTER_ROOT=C:\flutter "FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Desktop\carspart\windows\flutter\ephemeral" "PROJECT_DIR=C:\Users\<USER>\Desktop\carspart" "FLUTTER_TARGET=C:\Users\<USER>\Desktop\carspart\lib\main.dart" DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false "PACKAGE_CONFIG=C:\Users\<USER>\Desktop\carspart\.dart_tool\package_config.json" C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\CARSPART\BUILD\WINDOWS\X64\CMAKEFILES\E6D3D92FCD856A7653E205273136B783\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\CARSPART\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Desktop/carspart/windows" "-BC:/Users/<USER>/Desktop/carspart/build/windows/x64" --check-stamp-file "C:/Users/<USER>/Desktop/carspart/build/windows/x64/flutter/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
