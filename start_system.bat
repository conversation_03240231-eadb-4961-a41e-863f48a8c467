@echo off
title Cars Parts System - نظام قطع غيار السيارات
color 0A

echo.
echo ========================================
echo    نظام قطع غيار السيارات
echo    Cars Parts Management System
echo ========================================
echo.

echo [1] بدء تشغيل النظام الكامل
echo [2] تشغيل خادم PHP فقط
echo [3] تشغيل تطبيق Flutter فقط
echo [4] اختبار الاتصال
echo [5] فتح لوحة التحكم
echo [6] خروج
echo.

set /p choice="اختر رقم الخيار: "

if "%choice%"=="1" goto start_all
if "%choice%"=="2" goto start_php
if "%choice%"=="3" goto start_flutter
if "%choice%"=="4" goto test_connection
if "%choice%"=="5" goto open_admin
if "%choice%"=="6" goto exit

:start_all
echo.
echo جاري تشغيل النظام الكامل...
echo.

echo [1/2] تشغيل خادم PHP...
start "PHP Server" cmd /k "cd admin && php -S localhost:8000"
timeout /t 3 /nobreak >nul

echo [2/2] تشغيل تطبيق Flutter...
start "Flutter App" cmd /k "flutter run"

echo.
echo تم تشغيل النظام بنجاح!
echo.
echo الروابط المهمة:
echo - لوحة التحكم: http://localhost:8000/
echo - اختبار الخادم: http://localhost:8000/test.php
echo - اختبار قاعدة البيانات: http://localhost:8000/test_db.php
echo.
echo بيانات تسجيل الدخول:
echo - الوسيط: <EMAIL> / password
echo - العميل: <EMAIL> / password
echo - المحل: <EMAIL> / password
echo.
pause
goto menu

:start_php
echo.
echo تشغيل خادم PHP...
cd admin
php -S localhost:8000
pause
goto menu

:start_flutter
echo.
echo تشغيل تطبيق Flutter...
flutter run
pause
goto menu

:test_connection
echo.
echo اختبار الاتصال...
echo.
curl -s http://localhost:8000/test.php
echo.
echo.
curl -s http://localhost:8000/test_db.php
echo.
pause
goto menu

:open_admin
echo.
echo فتح لوحة التحكم...
start http://localhost:8000/
goto menu

:exit
echo.
echo شكراً لاستخدام النظام!
exit

:menu
cls
goto start
