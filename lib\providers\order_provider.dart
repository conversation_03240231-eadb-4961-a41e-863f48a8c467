import 'package:flutter/material.dart';
import '../models/order.dart';
import '../models/user.dart';

class OrderProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;

  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get orders based on user type
  List<Order> getOrdersForUser(User user) {
    switch (user.userType) {
      case UserType.customer:
        return _orders.where((order) => order.customerId == user.id).toList();
      case UserType.shop:
        // Shops see all orders to submit offers
        return _orders.where((order) => 
          order.status == OrderStatus.pending || 
          order.status == OrderStatus.hasOffers
        ).toList();
      case UserType.mediator:
        // Mediator sees all orders
        return _orders;
    }
  }

  Future<void> loadOrders() async {
    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Demo data
      _orders = [
        Order(
          id: '1',
          customerId: '1',
          customerName: 'أحمد محمد',
          title: 'مصباح أمامي لتويوتا كامري',
          description: 'أحتاج مصباح أمامي أيمن لتويوتا كامري موديل 2018، يفضل أن يكون أصلي',
          carModel: 'تويوتا كامري',
          carYear: '2018',
          images: [],
          status: OrderStatus.pending,
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        Order(
          id: '2',
          customerId: '2',
          customerName: 'فاطمة علي',
          title: 'فرامل لنيسان التيما',
          description: 'أحتاج طقم فرامل كامل لنيسان التيما 2020',
          carModel: 'نيسان التيما',
          carYear: '2020',
          images: [],
          status: OrderStatus.hasOffers,
          createdAt: DateTime.now().subtract(const Duration(hours: 5)),
          offers: [
            Offer(
              id: '1',
              orderId: '2',
              shopId: 'shop1',
              shopName: 'محل الشارقة لقطع الغيار',
              price: 150.0,
              description: 'فرامل أصلية من الوكيل',
              images: [],
              deliveryDays: 3,
              createdAt: DateTime.now().subtract(const Duration(hours: 1)),
            ),
          ],
        ),
      ];
      
      _setLoading(false);
    } catch (e) {
      _error = 'حدث خطأ أثناء تحميل الطلبات';
      _setLoading(false);
    }
  }

  Future<bool> createOrder({
    required String customerId,
    required String customerName,
    required String title,
    required String description,
    required String carModel,
    required String carYear,
    List<String> images = const [],
  }) async {
    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      final newOrder = Order(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        customerId: customerId,
        customerName: customerName,
        title: title,
        description: description,
        carModel: carModel,
        carYear: carYear,
        images: images,
        status: OrderStatus.pending,
        createdAt: DateTime.now(),
      );
      
      _orders.insert(0, newOrder);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'حدث خطأ أثناء إنشاء الطلب';
      _setLoading(false);
      return false;
    }
  }

  Future<bool> submitOffer({
    required String orderId,
    required String shopId,
    required String shopName,
    required double price,
    required String description,
    required int deliveryDays,
    List<String> images = const [],
  }) async {
    _setLoading(true);
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      final offer = Offer(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        orderId: orderId,
        shopId: shopId,
        shopName: shopName,
        price: price,
        description: description,
        images: images,
        deliveryDays: deliveryDays,
        createdAt: DateTime.now(),
      );
      
      // Find the order and add the offer
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        final order = _orders[orderIndex];
        final updatedOffers = List<Offer>.from(order.offers)..add(offer);
        
        _orders[orderIndex] = Order(
          id: order.id,
          customerId: order.customerId,
          customerName: order.customerName,
          title: order.title,
          description: order.description,
          carModel: order.carModel,
          carYear: order.carYear,
          images: order.images,
          status: OrderStatus.hasOffers,
          createdAt: order.createdAt,
          updatedAt: DateTime.now(),
          offers: updatedOffers,
          messages: order.messages,
          invoice: order.invoice,
        );
      }
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'حدث خطأ أثناء إرسال العرض';
      _setLoading(false);
      return false;
    }
  }

  Future<bool> sendMessage({
    required String orderId,
    required String senderId,
    required String senderName,
    required String senderType,
    required String content,
    List<String> images = const [],
  }) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      final message = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        orderId: orderId,
        senderId: senderId,
        senderName: senderName,
        senderType: senderType,
        content: content,
        images: images,
        createdAt: DateTime.now(),
      );
      
      // Find the order and add the message
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        final order = _orders[orderIndex];
        final updatedMessages = List<Message>.from(order.messages)..add(message);
        
        _orders[orderIndex] = Order(
          id: order.id,
          customerId: order.customerId,
          customerName: order.customerName,
          title: order.title,
          description: order.description,
          carModel: order.carModel,
          carYear: order.carYear,
          images: order.images,
          status: order.status,
          createdAt: order.createdAt,
          updatedAt: DateTime.now(),
          offers: order.offers,
          messages: updatedMessages,
          invoice: order.invoice,
        );
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'حدث خطأ أثناء إرسال الرسالة';
      return false;
    }
  }

  Order? getOrderById(String orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
