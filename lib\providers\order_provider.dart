import 'package:flutter/material.dart';
import '../models/order.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class OrderProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;

  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get orders based on user type
  List<Order> getOrdersForUser(User user) {
    switch (user.userType) {
      case UserType.customer:
        return _orders.where((order) => order.customerId == user.id).toList();
      case UserType.shop:
        // Shops see all orders to submit offers
        return _orders.where((order) => 
          order.status == OrderStatus.pending || 
          order.status == OrderStatus.hasOffers
        ).toList();
      case UserType.mediator:
        // Mediator sees all orders
        return _orders;
    }
  }

  Future<void> loadOrders(User user) async {
    _setLoading(true);
    try {
      final apiService = ApiService();
      final response = await apiService.getOrders(
        user.userType.toString().split('.').last,
        user.id,
      );

      if (response['success'] == true) {
        final ordersData = response['data'] as List;
        _orders = ordersData.map((orderJson) => Order.fromJson(orderJson)).toList();
      } else {
        _error = response['message'] ?? 'حدث خطأ أثناء تحميل الطلبات';
      }

      _setLoading(false);
    } catch (e) {
      _error = 'حدث خطأ في الاتصال بالخادم';
      _setLoading(false);
    }
  }

  Future<bool> createOrder({
    required String customerId,
    required String customerName,
    required String title,
    required String description,
    required String carModel,
    required String carYear,
    List<String> images = const [],
  }) async {
    _setLoading(true);
    try {
      final apiService = ApiService();
      final response = await apiService.createOrder({
        'customer_id': customerId,
        'customer_name': customerName,
        'title': title,
        'description': description,
        'car_model': carModel,
        'car_year': carYear,
        'images': images,
      });

      if (response['success'] == true) {
        final orderData = response['data'];
        final newOrder = Order.fromJson(orderData);
        _orders.insert(0, newOrder);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'حدث خطأ أثناء إنشاء الطلب';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'حدث خطأ في الاتصال بالخادم';
      _setLoading(false);
      return false;
    }
  }

  Future<bool> submitOffer({
    required String orderId,
    required String shopId,
    required String shopName,
    required double price,
    required String description,
    required int deliveryDays,
    List<String> images = const [],
  }) async {
    _setLoading(true);
    try {
      final apiService = ApiService();
      final response = await apiService.submitOffer({
        'order_id': orderId,
        'shop_id': shopId,
        'shop_name': shopName,
        'price': price,
        'description': description,
        'delivery_days': deliveryDays,
        'images': images,
      });

      if (response['success'] == true) {
        final offerData = response['data'];
        final offer = Offer.fromJson(offerData);

        // Find the order and add the offer
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          final order = _orders[orderIndex];
          final updatedOffers = List<Offer>.from(order.offers)..add(offer);

          _orders[orderIndex] = Order(
            id: order.id,
            customerId: order.customerId,
            customerName: order.customerName,
            title: order.title,
            description: order.description,
            carModel: order.carModel,
            carYear: order.carYear,
            images: order.images,
            status: OrderStatus.hasOffers,
            createdAt: order.createdAt,
            updatedAt: DateTime.now(),
            offers: updatedOffers,
            messages: order.messages,
            invoice: order.invoice,
          );
        }

        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'حدث خطأ أثناء إرسال العرض';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'حدث خطأ في الاتصال بالخادم';
      _setLoading(false);
      return false;
    }
  }

  Future<bool> sendMessage({
    required String orderId,
    required String senderId,
    required String senderName,
    required String senderType,
    required String content,
    List<String> images = const [],
  }) async {
    try {
      final apiService = ApiService();
      final response = await apiService.sendMessage({
        'order_id': orderId,
        'sender_id': senderId,
        'sender_name': senderName,
        'sender_type': senderType,
        'content': content,
        'images': images,
      });

      if (response['success'] == true) {
        final messageData = response['data'];
        final message = Message.fromJson(messageData);

        // Find the order and add the message
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          final order = _orders[orderIndex];
          final updatedMessages = List<Message>.from(order.messages)..add(message);

          _orders[orderIndex] = Order(
            id: order.id,
            customerId: order.customerId,
            customerName: order.customerName,
            title: order.title,
            description: order.description,
            carModel: order.carModel,
            carYear: order.carYear,
            images: order.images,
            status: order.status,
            createdAt: order.createdAt,
            updatedAt: DateTime.now(),
            offers: order.offers,
            messages: updatedMessages,
            invoice: order.invoice,
          );
        }

        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'حدث خطأ أثناء إرسال الرسالة';
        return false;
      }
    } catch (e) {
      _error = 'حدث خطأ في الاتصال بالخادم';
      return false;
    }
  }

  Order? getOrderById(String orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
