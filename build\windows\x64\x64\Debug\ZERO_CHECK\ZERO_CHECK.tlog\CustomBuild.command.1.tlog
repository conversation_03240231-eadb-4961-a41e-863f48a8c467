^C:\USERS\<USER>\DESKTOP\CARSPART\BUILD\WINDOWS\X64\CMAKEFILES\7C01439F9DD97E25E13E8F5C68624B52\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Desktop/carspart/windows" "-BC:/Users/<USER>/Desktop/carspart/build/windows/x64" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "C:/Users/<USER>/Desktop/carspart/build/windows/x64/carspart.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
