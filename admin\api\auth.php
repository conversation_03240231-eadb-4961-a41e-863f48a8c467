<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];
$request = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'POST':
            $action = $_GET['action'] ?? '';
            
            if ($action === 'login') {
                handleLogin($db, $request);
            } elseif ($action === 'register') {
                handleRegister($db, $request);
            } else {
                errorResponse('Invalid action', 400);
            }
            break;
            
        default:
            errorResponse('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    errorResponse('Server error: ' . $e->getMessage(), 500);
}

function handleLogin($db, $request) {
    $email = sanitize($request['email'] ?? '');
    $password = $request['password'] ?? '';
    $userType = sanitize($request['user_type'] ?? '');
    
    if (empty($email) || empty($password) || empty($userType)) {
        errorResponse('البريد الإلكتروني وكلمة المرور ونوع المستخدم مطلوبة');
    }
    
    $user = $db->fetch(
        "SELECT * FROM users WHERE email = ? AND user_type = ? AND is_active = 1",
        [$email, $userType]
    );
    
    if (!$user || !verifyPassword($password, $user['password'])) {
        errorResponse('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }
    
    // إنشاء token بسيط (في الإنتاج استخدم JWT)
    $token = generateToken();
    
    // حفظ التوكن في قاعدة البيانات (يمكن إضافة جدول للتوكنات)
    
    $userData = [
        'id' => $user['id'],
        'name' => $user['name'],
        'email' => $user['email'],
        'phone' => $user['phone'],
        'user_type' => $user['user_type'],
        'profile_image' => $user['profile_image'],
        'created_at' => $user['created_at']
    ];
    
    successResponse([
        'user' => $userData,
        'token' => $token
    ], 'تم تسجيل الدخول بنجاح');
}

function handleRegister($db, $request) {
    $name = sanitize($request['name'] ?? '');
    $email = sanitize($request['email'] ?? '');
    $phone = sanitize($request['phone'] ?? '');
    $password = $request['password'] ?? '';
    $userType = sanitize($request['user_type'] ?? '');
    
    if (empty($name) || empty($email) || empty($phone) || empty($password) || empty($userType)) {
        errorResponse('جميع الحقول مطلوبة');
    }
    
    if (!validateEmail($email)) {
        errorResponse('البريد الإلكتروني غير صحيح');
    }
    
    if (strlen($password) < 6) {
        errorResponse('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }
    
    // التحقق من عدم وجود المستخدم مسبقاً
    $existingUser = $db->fetch("SELECT id FROM users WHERE email = ?", [$email]);
    if ($existingUser) {
        errorResponse('البريد الإلكتروني مستخدم مسبقاً');
    }
    
    // إنشاء المستخدم الجديد
    $hashedPassword = hashPassword($password);
    
    $userId = $db->insert('users', [
        'name' => $name,
        'email' => $email,
        'phone' => $phone,
        'password' => $hashedPassword,
        'user_type' => $userType
    ]);
    
    // جلب بيانات المستخدم الجديد
    $user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);
    
    $token = generateToken();
    
    $userData = [
        'id' => $user['id'],
        'name' => $user['name'],
        'email' => $user['email'],
        'phone' => $user['phone'],
        'user_type' => $user['user_type'],
        'profile_image' => $user['profile_image'],
        'created_at' => $user['created_at']
    ];
    
    successResponse([
        'user' => $userData,
        'token' => $token
    ], 'تم إنشاء الحساب بنجاح');
}
?>
