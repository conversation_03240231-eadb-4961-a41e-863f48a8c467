<?php
/**
 * إعدادات قاعدة البيانات
 * Database Configuration
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'carspart_db';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $conn;

    /**
     * الاتصال بقاعدة البيانات
     */
    public function connect() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $e) {
            echo "خطأ في الاتصال: " . $e->getMessage();
        }
        
        return $this->conn;
    }

    /**
     * إغلاق الاتصال
     */
    public function disconnect() {
        $this->conn = null;
    }

    /**
     * تنفيذ استعلام
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            throw new Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }

    /**
     * الحصول على سجل واحد
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * الحصول على جميع السجلات
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * إدراج سجل جديد
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->conn->lastInsertId();
    }

    /**
     * تحديث سجل
     */
    public function update($table, $data, $where, $whereParams = []) {
        $set = [];
        foreach($data as $key => $value) {
            $set[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $set);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->query($sql, $params);
    }

    /**
     * حذف سجل
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }

    /**
     * عدد السجلات
     */
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->fetch($sql, $params);
        return $result['count'];
    }

    /**
     * التحقق من وجود سجل
     */
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }

    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->conn->rollback();
    }
}

/**
 * إنشاء اتصال عام
 */
function getDB() {
    static $db = null;
    if ($db === null) {
        $db = new Database();
        $db->connect();
    }
    return $db;
}

/**
 * تنظيف البيانات
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * إنشاء رمز عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * تنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d H:i:s') {
    if ($date instanceof DateTime) {
        return $date->format($format);
    }
    return date($format, strtotime($date));
}

/**
 * تنسيق المبلغ
 */
function formatPrice($price, $currency = 'ر.ع') {
    return number_format($price, 3) . ' ' . $currency;
}

/**
 * رفع ملف
 */
function uploadFile($file, $uploadDir = 'uploads/', $allowedTypes = ['jpg', 'jpeg', 'png', 'gif']) {
    if (!isset($file['error']) || $file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('خطأ في رفع الملف');
    }

    $fileInfo = pathinfo($file['name']);
    $extension = strtolower($fileInfo['extension']);
    
    if (!in_array($extension, $allowedTypes)) {
        throw new Exception('نوع الملف غير مسموح');
    }

    $fileName = uniqid() . '.' . $extension;
    $filePath = $uploadDir . $fileName;
    
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        throw new Exception('فشل في حفظ الملف');
    }

    return $filePath;
}

/**
 * إرسال استجابة JSON
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * إرسال رسالة خطأ
 */
function errorResponse($message, $statusCode = 400) {
    jsonResponse(['error' => $message], $statusCode);
}

/**
 * إرسال رسالة نجاح
 */
function successResponse($data = [], $message = 'تم بنجاح') {
    jsonResponse(['success' => true, 'message' => $message, 'data' => $data]);
}
?>
