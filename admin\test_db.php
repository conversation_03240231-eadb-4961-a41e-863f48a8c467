<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

require_once 'config/database.php';

try {
    $db = getDB();
    
    // اختبار الاتصال
    $result = $db->fetch("SELECT COUNT(*) as count FROM users");
    
    // جلب بعض البيانات التجريبية
    $users = $db->fetchAll("SELECT id, name, email, user_type FROM users LIMIT 5");
    $orders = $db->fetchAll("SELECT id, title, status FROM orders LIMIT 5");
    
    echo json_encode([
        'success' => true,
        'message' => 'Database connection successful!',
        'data' => [
            'users_count' => $result['count'],
            'sample_users' => $users,
            'sample_orders' => $orders
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
