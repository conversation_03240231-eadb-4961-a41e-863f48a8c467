# 🚗 نظام قطع غيار السيارات - Cars Parts Management System

نظام متكامل لإدارة طلبات قطع غيار السيارات يربط بين العملاء العمانيين والموردين في الشارقة عبر وسيط.

## 🌟 نظرة عامة

هذا النظام يتيح للعملاء في عُمان طلب قطع غيار السيارات من الموردين في الشارقة عبر وسيط يدير العملية ويحصل على عمولة.

### 🎯 سير العمل:
1. **العميل** ينشر طلب قطعة غيار
2. **المحلات** تقدم عروضها (غير مرئية للعميل)
3. **الوسيط** يراجع العروض ويتواصل مع العميل
4. **الوسيط** يختار أفضل عرض ويضيف عمولته
5. **العميل** يحول المبلغ ويرسل وصل التحويل

## 🏗️ مكونات النظام

### 📱 تطبيق Flutter (3 واجهات)
- **واجهة العملاء:** طلب قطع غيار + محادثة مع الوسيط
- **واجهة المحلات:** مشاهدة الطلبات + تقديم العروض
- **واجهة الوسيط:** إدارة شاملة للطلبات والعروض

### 💻 لوحة تحكم PHP/MySQL
- إدارة الطلبات والعروض والفواتير
- نظام محادثات مع العملاء
- حساب العمولات التلقائي
- تقارير الأرباح والإحصائيات

### 🔗 API متكامل
- ربط كامل بين التطبيق وقاعدة البيانات
- نقاط نهاية لجميع العمليات
- معالجة الأخطاء والاستجابات

## 🚀 التشغيل السريع

### 1️⃣ تشغيل تلقائي (الأسهل)
```bash
# تشغيل النظام بالكامل
start_system.bat
```

### 2️⃣ تشغيل يدوي
```bash
# 1. إعداد قاعدة البيانات
mysql -u root -p carspart_db < admin/database/carspart_db.sql

# 2. تشغيل خادم PHP
cd admin
php -S localhost:8000

# 3. تشغيل تطبيق Flutter
cd carspart
flutter pub get
flutter run
```

## 🔑 بيانات تسجيل الدخول

### لوحة التحكم PHP
- **الرابط:** http://localhost:8000/
- **البريد:** <EMAIL>
- **كلمة المرور:** password

### تطبيق Flutter
| النوع | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| عميل | <EMAIL> | password |
| محل | <EMAIL> | password |
| وسيط | <EMAIL> | password |

## 📋 المتطلبات

- **PHP 7.4+** مع PDO
- **MySQL 5.7+**
- **Flutter SDK**
- **محرر أكواد** (VS Code مُفضل)

## 🎯 الميزات الرئيسية

### ✅ للعملاء:
- طلب قطع غيار بسهولة
- رفع صور القطع المطلوبة
- محادثة مباشرة مع الوسيط
- استلام الفواتير ورفع وصولات التحويل

### ✅ للمحلات:
- مشاهدة الطلبات الجديدة
- تقديم عروض تنافسية
- رفع صور القطع المتوفرة
- تحديد مدة التوصيل

### ✅ للوسيط:
- إدارة شاملة لجميع الطلبات
- مقارنة العروض واختيار الأفضل
- حساب العمولات تلقائياً
- إنشاء فواتير احترافية
- تتبع المدفوعات والأرباح

## 📁 هيكل المشروع

```
carspart/
├── lib/                    # تطبيق Flutter
│   ├── models/            # نماذج البيانات
│   ├── providers/         # إدارة الحالة
│   ├── screens/           # شاشات التطبيق
│   └── services/          # خدمات API
├── admin/                 # لوحة تحكم PHP
│   ├── api/              # نقاط نهاية API
│   ├── config/           # إعدادات قاعدة البيانات
│   ├── database/         # ملفات قاعدة البيانات
│   └── assets/           # ملفات CSS/JS
├── QUICK_START.md        # دليل التشغيل السريع
├── FINAL_SETUP.md        # الإعداد النهائي
└── start_system.bat      # تشغيل تلقائي
```

## 🔧 التخصيص

### تغيير عنوان API:
```dart
// في lib/services/api_service.dart
static const String baseUrl = 'http://your-server.com/admin/api';
```

### تغيير إعدادات قاعدة البيانات:
```php
// في admin/config/database.php
private $host = 'your-host';
private $db_name = 'your-database';
private $username = 'your-username';
private $password = 'your-password';
```

## 🛠️ استكشاف الأخطاء

### اختبار الاتصال:
- **خادم PHP:** http://localhost:8000/test.php
- **قاعدة البيانات:** http://localhost:8000/test_db.php
- **في التطبيق:** اضغط "اختبار الاتصال بالخادم"

### مشاكل شائعة:
1. **خطأ في الاتصال:** تأكد من تشغيل خادم PHP
2. **خطأ قاعدة البيانات:** تحقق من إنشاء قاعدة البيانات
3. **مشاكل CORS:** تأكد من headers في ملفات API

## 📚 الوثائق

- [دليل التشغيل السريع](QUICK_START.md)
- [الإعداد النهائي](FINAL_SETUP.md)
- [وثائق لوحة التحكم](admin/README.md)

## 🤝 المساهمة

1. Fork المشروع
2. أنشئ branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للـ branch (`git push origin feature/amazing-feature`)
5. افتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للحصول على الدعم:
- راجع ملفات الوثائق
- تحقق من استكشاف الأخطاء
- تأكد من تشغيل جميع الخدمات

---

**🎉 مبروك! لديك الآن نظام متكامل لإدارة قطع غيار السيارات**
